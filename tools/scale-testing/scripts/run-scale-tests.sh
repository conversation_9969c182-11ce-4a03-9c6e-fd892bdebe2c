#!/bin/bash

# EPP Scale Testing Execution Script
# This script orchestrates the execution of comprehensive EPP scale tests

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
SCALE_TEST_DIR="${PROJECT_ROOT}/scale-testing"

# Default configuration
DEFAULT_NAMESPACE="default"
DEFAULT_CONFIG_FILE="${SCALE_TEST_DIR}/configs/test-matrix.yaml"
DEFAULT_RESOURCE_CONFIG_FILE="${SCALE_TEST_DIR}/configs/resource-configs.yaml"
DEFAULT_RESULTS_DIR="${SCALE_TEST_DIR}/results"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

EPP Scale Testing Execution Script

OPTIONS:
    -h, --help                  Show this help message
    -n, --namespace NAMESPACE   Kubernetes namespace (default: ${DEFAULT_NAMESPACE})
    -c, --config FILE          Test matrix configuration file (default: ${DEFAULT_CONFIG_FILE})
    -r, --resource-config FILE Resource configuration file (default: ${DEFAULT_RESOURCE_CONFIG_FILE})
    -o, --output-dir DIR       Results output directory (default: ${DEFAULT_RESULTS_DIR})
    --dry-run                  Show what would be executed without running tests
    --skip-validation          Skip pre-test validation
    --resource-config-only CONFIG  Run tests only for specific resource configuration
    --prompt-length-only LENGTH    Run tests only for specific prompt length
    --qps-only QPS             Run tests only for specific QPS level
    --continue-on-failure      Continue testing even if individual tests fail
    --cleanup-on-exit          Cleanup resources on script exit
    --verbose                  Enable verbose output

EXAMPLES:
    # Run all scale tests
    $0

    # Run tests for specific resource configuration
    $0 --resource-config-only default

    # Run tests with custom configuration
    $0 --config /path/to/custom-config.yaml --output-dir /path/to/results

    # Dry run to see what would be executed
    $0 --dry-run

EOF
}

# Parse command line arguments
parse_args() {
    NAMESPACE="${DEFAULT_NAMESPACE}"
    CONFIG_FILE="${DEFAULT_CONFIG_FILE}"
    RESOURCE_CONFIG_FILE="${DEFAULT_RESOURCE_CONFIG_FILE}"
    RESULTS_DIR="${DEFAULT_RESULTS_DIR}"
    DRY_RUN=false
    SKIP_VALIDATION=false
    RESOURCE_CONFIG_FILTER=""
    PROMPT_LENGTH_FILTER=""
    QPS_FILTER=""
    CONTINUE_ON_FAILURE=false
    CLEANUP_ON_EXIT=false
    VERBOSE=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                usage
                exit 0
                ;;
            -n|--namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -r|--resource-config)
                RESOURCE_CONFIG_FILE="$2"
                shift 2
                ;;
            -o|--output-dir)
                RESULTS_DIR="$2"
                shift 2
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --skip-validation)
                SKIP_VALIDATION=true
                shift
                ;;
            --resource-config-only)
                RESOURCE_CONFIG_FILTER="$2"
                shift 2
                ;;
            --prompt-length-only)
                PROMPT_LENGTH_FILTER="$2"
                shift 2
                ;;
            --qps-only)
                QPS_FILTER="$2"
                shift 2
                ;;
            --continue-on-failure)
                CONTINUE_ON_FAILURE=true
                shift
                ;;
            --cleanup-on-exit)
                CLEANUP_ON_EXIT=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
}

# Validation functions
validate_prerequisites() {
    log_info "Validating prerequisites..."

    # Check required tools
    local required_tools=("kubectl" "python3" "yq")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool not found: $tool"
            return 1
        fi
    done

    # Check configuration files
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "Configuration file not found: $CONFIG_FILE"
        return 1
    fi

    if [[ ! -f "$RESOURCE_CONFIG_FILE" ]]; then
        log_error "Resource configuration file not found: $RESOURCE_CONFIG_FILE"
        return 1
    fi

    # Check Kubernetes connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        return 1
    fi

    # Check namespace exists
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_error "Namespace does not exist: $NAMESPACE"
        return 1
    fi

    # Check EPP deployment exists
    if ! kubectl get deployment vllm-llama3-8b-instruct-epp -n "$NAMESPACE" &> /dev/null; then
        log_error "EPP deployment not found in namespace: $NAMESPACE"
        return 1
    fi

    # Check llm-d sim server is available
    if ! kubectl get deployment vllm-llama3-8b-instruct -n "$NAMESPACE" &> /dev/null; then
        log_error "Model server deployment not found in namespace: $NAMESPACE"
        return 1
    fi

    log_success "Prerequisites validation passed"
    return 0
}

# Get gateway IP for testing
get_gateway_ip() {
    local gateway_ip
    gateway_ip=$(kubectl get gateway/inference-gateway -n "$NAMESPACE" -o jsonpath='{.status.addresses[0].value}' 2>/dev/null || echo "")
    
    if [[ -z "$gateway_ip" ]]; then
        log_error "Could not get gateway IP address"
        return 1
    fi
    
    echo "$gateway_ip"
}

# Load test configuration
load_test_config() {
    log_info "Loading test configuration from $CONFIG_FILE"
    
    # Use Python to parse YAML and extract test parameters
    python3 << EOF
import yaml
import json
import sys

try:
    with open('$CONFIG_FILE', 'r') as f:
        config = yaml.safe_load(f)
    
    # Extract test parameters
    resource_configs = config.get('resource_configs', [])
    prompt_lengths = config.get('prompt_lengths', [])
    qps_levels = config.get('qps_levels', [])
    
    # Apply filters if specified
    if '$RESOURCE_CONFIG_FILTER':
        resource_configs = [rc for rc in resource_configs if rc['name'] == '$RESOURCE_CONFIG_FILTER']
    
    if '$PROMPT_LENGTH_FILTER':
        prompt_lengths = [pl for pl in prompt_lengths if pl['name'] == '$PROMPT_LENGTH_FILTER']
    
    if '$QPS_FILTER':
        qps_levels = [ql for ql in qps_levels if ql['name'] == '$QPS_FILTER']
    
    # Output configuration for shell consumption
    print(f"RESOURCE_CONFIGS='{json.dumps([rc['name'] for rc in resource_configs])}'")
    print(f"PROMPT_LENGTHS='{json.dumps([(pl['name'], pl['tokens']) for pl in prompt_lengths])}'")
    print(f"QPS_LEVELS='{json.dumps([(ql['name'], ql['qps']) for ql in qps_levels])}'")
    print(f"TEST_DURATION={config.get('execution', {}).get('test_duration_seconds', 300)}")
    
except Exception as e:
    print(f"Error loading configuration: {e}", file=sys.stderr)
    sys.exit(1)
EOF
}

# Execute a single test
execute_test() {
    local resource_config="$1"
    local prompt_length_name="$2"
    local prompt_length_tokens="$3"
    local qps_name="$4"
    local qps_value="$5"
    local gateway_ip="$6"
    
    log_info "Executing test: ${resource_config}/${prompt_length_name}/${qps_name}"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would execute test with:"
        log_info "  Resource Config: $resource_config"
        log_info "  Prompt Length: $prompt_length_tokens tokens ($prompt_length_name)"
        log_info "  QPS: $qps_value ($qps_name)"
        log_info "  Gateway IP: $gateway_ip"
        return 0
    fi
    
    # Update EPP resource configuration
    log_info "Updating EPP resource configuration to: $resource_config"
    if ! python3 "${SCRIPT_DIR}/epp-resource-manager.py" \
        --config-name "$resource_config" \
        --config-path "$RESOURCE_CONFIG_FILE" \
        --namespace "$NAMESPACE" \
        ${VERBOSE:+--verbose}; then
        log_error "Failed to update EPP resource configuration"
        return 1
    fi
    
    # Wait for EPP to stabilize
    log_info "Waiting for EPP to stabilize..."
    sleep 30
    
    # Execute benchmark test
    log_info "Starting benchmark test..."
    local benchmark_id="epp-scale-${resource_config}-${prompt_length_name}-${qps_name}"
    
    # Create temporary benchmark configuration
    local temp_benchmark_config="/tmp/benchmark-${benchmark_id}.yaml"
    sed -e "s/\${TARGET_IP}/$gateway_ip/g" \
        -e "s/\${QPS_VALUE}/$qps_value/g" \
        -e "s/\${TEST_DURATION}/$TEST_DURATION/g" \
        -e "s/\${PROMPT_LENGTH}/$prompt_length_tokens/g" \
        -e "s/\${PROMPT_LENGTH_NAME}/$prompt_length_name/g" \
        -e "s/\${QPS_NAME}/$qps_name/g" \
        -e "s/\${RESOURCE_CONFIG}/$resource_config/g" \
        "${SCALE_TEST_DIR}/configs/benchmark-templates/scale-test-template.yaml" > "$temp_benchmark_config"
    
    # Apply benchmark configuration
    if ! kubectl apply -f "$temp_benchmark_config"; then
        log_error "Failed to apply benchmark configuration"
        rm -f "$temp_benchmark_config"
        return 1
    fi
    
    # Wait for benchmark to complete
    log_info "Waiting for benchmark to complete..."
    if ! benchmark_id="$benchmark_id" "${PROJECT_ROOT}/benchmark/download-benchmark-results.bash"; then
        log_error "Benchmark execution failed"
        kubectl delete -f "$temp_benchmark_config" --ignore-not-found=true
        rm -f "$temp_benchmark_config"
        return 1
    fi
    
    # Cleanup
    kubectl delete -f "$temp_benchmark_config" --ignore-not-found=true
    rm -f "$temp_benchmark_config"
    
    log_success "Test completed: ${resource_config}/${prompt_length_name}/${qps_name}"
    return 0
}

# Main execution function
main() {
    parse_args "$@"
    
    log_info "Starting EPP Scale Testing"
    log_info "Configuration: $CONFIG_FILE"
    log_info "Resource Config: $RESOURCE_CONFIG_FILE"
    log_info "Results Directory: $RESULTS_DIR"
    log_info "Namespace: $NAMESPACE"
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    
    # Validate prerequisites
    if [[ "$SKIP_VALIDATION" != "true" ]]; then
        if ! validate_prerequisites; then
            log_error "Prerequisites validation failed"
            exit 1
        fi
    fi
    
    # Get gateway IP
    local gateway_ip
    if ! gateway_ip=$(get_gateway_ip); then
        log_error "Failed to get gateway IP"
        exit 1
    fi
    log_info "Using gateway IP: $gateway_ip"
    
    # Load test configuration
    eval "$(load_test_config)"
    
    # Parse JSON arrays
    local resource_configs_array
    local prompt_lengths_array
    local qps_levels_array
    
    resource_configs_array=$(echo "$RESOURCE_CONFIGS" | python3 -c "import json, sys; print(' '.join(json.load(sys.stdin)))")
    prompt_lengths_array=$(echo "$PROMPT_LENGTHS" | python3 -c "import json, sys; [print(f'{name}:{tokens}') for name, tokens in json.load(sys.stdin)]")
    qps_levels_array=$(echo "$QPS_LEVELS" | python3 -c "import json, sys; [print(f'{name}:{qps}') for name, qps in json.load(sys.stdin)]")
    
    # Execute tests
    local total_tests=0
    local successful_tests=0
    local failed_tests=0
    
    for resource_config in $resource_configs_array; do
        for prompt_length_spec in $prompt_lengths_array; do
            IFS=':' read -r prompt_length_name prompt_length_tokens <<< "$prompt_length_spec"
            
            for qps_spec in $qps_levels_array; do
                IFS=':' read -r qps_name qps_value <<< "$qps_spec"
                
                ((total_tests++))
                
                if execute_test "$resource_config" "$prompt_length_name" "$prompt_length_tokens" "$qps_name" "$qps_value" "$gateway_ip"; then
                    ((successful_tests++))
                else
                    ((failed_tests++))
                    if [[ "$CONTINUE_ON_FAILURE" != "true" ]]; then
                        log_error "Test failed, stopping execution (use --continue-on-failure to continue)"
                        break 3
                    fi
                fi
            done
        done
    done
    
    # Summary
    log_info "Scale testing completed"
    log_info "Total tests: $total_tests"
    log_success "Successful tests: $successful_tests"
    if [[ $failed_tests -gt 0 ]]; then
        log_error "Failed tests: $failed_tests"
    fi
    
    if [[ $failed_tests -eq 0 ]]; then
        log_success "All tests completed successfully!"
        exit 0
    else
        log_error "Some tests failed"
        exit 1
    fi
}

# Cleanup function
cleanup() {
    if [[ "$CLEANUP_ON_EXIT" == "true" ]]; then
        log_info "Cleaning up resources..."
        kubectl delete deployment epp-scale-test-benchmark --ignore-not-found=true -n "$NAMESPACE"
        kubectl delete job -l app=epp-scale-test --ignore-not-found=true -n "$NAMESPACE"
    fi
}

# Set up cleanup trap
trap cleanup EXIT

# Run main function
main "$@"
