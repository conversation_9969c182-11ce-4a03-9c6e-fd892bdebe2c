#!/usr/bin/env python3
"""
EPP Resource Manager for Scale Testing

This script manages EPP deployment resource configurations during scale testing.
It can dynamically update EPP deployments with different resource limits and
monitor deployment readiness.
"""

import argparse
import json
import logging
import subprocess
import time
import yaml
from pathlib import Path
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EPPResourceManager:
    """Manages EPP deployment resource configurations for scale testing."""
    
    def __init__(self, namespace: str = "default", deployment_name: str = "vllm-llama3-8b-instruct-epp"):
        self.namespace = namespace
        self.deployment_name = deployment_name
        self.kubectl_timeout = 300  # 5 minutes timeout for kubectl operations
    
    def run_kubectl(self, args: list, capture_output: bool = True) -> subprocess.CompletedProcess:
        """Run kubectl command with error handling."""
        cmd = ["kubectl"] + args
        logger.debug(f"Running command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=capture_output,
                text=True,
                timeout=self.kubectl_timeout,
                check=True
            )
            return result
        except subprocess.CalledProcessError as e:
            logger.error(f"kubectl command failed: {e}")
            logger.error(f"stdout: {e.stdout}")
            logger.error(f"stderr: {e.stderr}")
            raise
        except subprocess.TimeoutExpired as e:
            logger.error(f"kubectl command timed out: {e}")
            raise
    
    def get_current_deployment(self) -> Optional[Dict[str, Any]]:
        """Get current EPP deployment configuration."""
        try:
            result = self.run_kubectl([
                "get", "deployment", self.deployment_name,
                "-n", self.namespace,
                "-o", "json"
            ])
            return json.loads(result.stdout)
        except subprocess.CalledProcessError:
            logger.warning(f"Deployment {self.deployment_name} not found in namespace {self.namespace}")
            return None
    
    def apply_resource_config(self, config_name: str, config_path: Path) -> bool:
        """Apply a specific resource configuration to the EPP deployment."""
        logger.info(f"Applying resource configuration: {config_name}")
        
        # Load the resource configuration
        try:
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load configuration file {config_path}: {e}")
            return False
        
        # Extract the specific configuration
        if config_name not in config_data.get('data', {}):
            logger.error(f"Configuration {config_name} not found in {config_path}")
            return False
        
        deployment_yaml = config_data['data'][f'{config_name}.yaml']
        
        # Apply the configuration
        try:
            process = subprocess.Popen(
                ["kubectl", "apply", "-f", "-"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate(input=deployment_yaml)
            
            if process.returncode != 0:
                logger.error(f"Failed to apply configuration: {stderr}")
                return False
            
            logger.info(f"Successfully applied configuration: {config_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error applying configuration: {e}")
            return False
    
    def wait_for_deployment_ready(self, timeout: int = 300) -> bool:
        """Wait for EPP deployment to be ready after configuration change."""
        logger.info(f"Waiting for deployment {self.deployment_name} to be ready...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                result = self.run_kubectl([
                    "get", "deployment", self.deployment_name,
                    "-n", self.namespace,
                    "-o", "jsonpath={.status.readyReplicas}"
                ])
                
                ready_replicas = result.stdout.strip()
                if ready_replicas and int(ready_replicas) > 0:
                    logger.info(f"Deployment {self.deployment_name} is ready")
                    return True
                
            except (subprocess.CalledProcessError, ValueError):
                pass
            
            logger.debug("Deployment not ready yet, waiting...")
            time.sleep(10)
        
        logger.error(f"Deployment {self.deployment_name} did not become ready within {timeout} seconds")
        return False
    
    def get_deployment_resources(self) -> Optional[Dict[str, Any]]:
        """Get current resource configuration of the EPP deployment."""
        deployment = self.get_current_deployment()
        if not deployment:
            return None
        
        try:
            container = deployment['spec']['template']['spec']['containers'][0]
            resources = container.get('resources', {})
            
            return {
                'limits': resources.get('limits', {}),
                'requests': resources.get('requests', {}),
                'container_name': container.get('name', 'unknown')
            }
        except (KeyError, IndexError) as e:
            logger.error(f"Failed to extract resource information: {e}")
            return None
    
    def validate_deployment_health(self) -> bool:
        """Validate that the EPP deployment is healthy and responding."""
        logger.info("Validating EPP deployment health...")
        
        # Check deployment status
        try:
            result = self.run_kubectl([
                "get", "deployment", self.deployment_name,
                "-n", self.namespace,
                "-o", "jsonpath={.status.conditions[?(@.type=='Available')].status}"
            ])
            
            if result.stdout.strip() != "True":
                logger.error("Deployment is not available")
                return False
            
        except subprocess.CalledProcessError:
            logger.error("Failed to check deployment status")
            return False
        
        # Check pod health
        try:
            result = self.run_kubectl([
                "get", "pods",
                "-l", f"app={self.deployment_name}",
                "-n", self.namespace,
                "-o", "jsonpath={.items[0].status.phase}"
            ])
            
            if result.stdout.strip() != "Running":
                logger.error("Pod is not running")
                return False
            
        except subprocess.CalledProcessError:
            logger.error("Failed to check pod status")
            return False
        
        logger.info("EPP deployment is healthy")
        return True
    
    def update_and_wait(self, config_name: str, config_path: Path) -> bool:
        """Update EPP deployment with new resource configuration and wait for readiness."""
        logger.info(f"Updating EPP deployment with configuration: {config_name}")
        
        # Record current resources for logging
        current_resources = self.get_deployment_resources()
        if current_resources:
            logger.info(f"Current resources: {current_resources}")
        
        # Apply new configuration
        if not self.apply_resource_config(config_name, config_path):
            return False
        
        # Wait for deployment to be ready
        if not self.wait_for_deployment_ready():
            return False
        
        # Validate deployment health
        if not self.validate_deployment_health():
            return False
        
        # Log new resources
        new_resources = self.get_deployment_resources()
        if new_resources:
            logger.info(f"New resources: {new_resources}")
        
        logger.info(f"Successfully updated EPP deployment to configuration: {config_name}")
        return True


def main():
    parser = argparse.ArgumentParser(description="Manage EPP deployment resource configurations")
    parser.add_argument("--config-name", required=True, help="Resource configuration name")
    parser.add_argument("--config-path", required=True, type=Path, help="Path to resource configurations file")
    parser.add_argument("--namespace", default="default", help="Kubernetes namespace")
    parser.add_argument("--deployment-name", default="vllm-llama3-8b-instruct-epp", help="EPP deployment name")
    parser.add_argument("--timeout", type=int, default=300, help="Timeout for deployment readiness")
    parser.add_argument("--validate-only", action="store_true", help="Only validate current deployment health")
    parser.add_argument("--get-resources", action="store_true", help="Get current resource configuration")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    manager = EPPResourceManager(args.namespace, args.deployment_name)
    
    if args.validate_only:
        success = manager.validate_deployment_health()
        exit(0 if success else 1)
    
    if args.get_resources:
        resources = manager.get_deployment_resources()
        if resources:
            print(json.dumps(resources, indent=2))
            exit(0)
        else:
            logger.error("Failed to get resource configuration")
            exit(1)
    
    # Update deployment with new configuration
    success = manager.update_and_wait(args.config_name, args.config_path)
    exit(0 if success else 1)


if __name__ == "__main__":
    main()
