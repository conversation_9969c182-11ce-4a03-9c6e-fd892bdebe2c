# EPP Scale Testing Framework

This framework provides comprehensive scale testing capabilities for the Endpoint Picker Plugin (EPP) component of the Gateway API Inference Extension. It enables testing across multiple dimensions including prompt length, QPS (Queries Per Second), and resource configurations to determine optimal EPP performance characteristics.

## Overview

The EPP Scale Testing Framework addresses [Issue #1123](https://github.com/kubernetes-sigs/gateway-api-inference-extension/issues/1123) by providing:

- **Multi-dimensional testing**: Prompt length, QPS, and resource configuration combinations
- **Pareto frontier analysis**: Trade-off analysis between prompt length and QPS performance
- **Resource documentation**: Complete recording of EPP container resource limits during testing
- **Automated execution**: Full test automation with comprehensive result collection
- **Performance analysis**: Detailed performance reports and recommendations

## Architecture

```
tools/scale-testing/
├── configs/                    # Test configurations
│   ├── test-matrix.yaml       # Test parameter matrix
│   ├── resource-configs.yaml  # EPP resource configurations
│   └── benchmark-templates/   # LPG benchmark templates
├── scripts/                   # Execution scripts
│   ├── run-scale-tests.sh    # Main test orchestration
│   └── epp-resource-manager.py # EPP resource management
├── analysis/                  # Analysis tools
│   ├── pareto-analysis.py    # Pareto frontier analysis
│   └── performance-reports.py # Performance reporting
├── results/                   # Test results (created during execution)
└── requirements.txt          # Python dependencies
```

## Prerequisites

### System Requirements

- Kubernetes cluster with sufficient resources for testing
- `kubectl` configured and connected to the cluster
- Python 3.8+ with pip
- `yq` command-line YAML processor

### Deployed Components

- Gateway API Inference Extension with EPP deployment
- llm-d sim server deployment (vllm-llama3-8b-instruct)
- Inference Gateway configured and accessible
- Prometheus metrics collection (optional but recommended)

### Installation

1. **Install Python dependencies:**
   ```bash
   cd tools/scale-testing
   pip install -r requirements.txt
   ```

2. **Verify prerequisites:**
   ```bash
   ./scripts/run-scale-tests.sh --help
   ```

## Configuration

### Test Matrix Configuration

Edit `configs/test-matrix.yaml` to customize test parameters:

```yaml
# Prompt length dimensions (tokens)
prompt_lengths:
  - name: "small"
    tokens: 512
  - name: "medium" 
    tokens: 1024
  - name: "large"
    tokens: 2048

# QPS test levels
qps_levels:
  - name: "low-50"
    qps: 50
  - name: "medium-200"
    qps: 200
  - name: "high-500"
    qps: 500

# EPP resource configurations
resource_configs:
  - name: "default"
    cpu_limit: "1000m"
    memory_limit: "1Gi"
  - name: "high"
    cpu_limit: "2000m"
    memory_limit: "2Gi"
```

### Resource Configurations

The framework tests EPP with different resource allocations defined in `configs/resource-configs.yaml`:

- **minimal**: 500m CPU, 512Mi memory
- **default**: 1000m CPU, 1Gi memory  
- **high**: 2000m CPU, 2Gi memory
- **maximum**: 4000m CPU, 4Gi memory

## Usage

### Basic Usage

Run all scale tests with default configuration:

```bash
./scripts/run-scale-tests.sh
```

### Advanced Usage

**Run tests for specific resource configuration:**
```bash
./scripts/run-scale-tests.sh --resource-config-only default
```

**Run tests with custom configuration:**
```bash
./scripts/run-scale-tests.sh \
  --config /path/to/custom-config.yaml \
  --output-dir /path/to/results
```

**Dry run to preview test execution:**
```bash
./scripts/run-scale-tests.sh --dry-run
```

**Continue testing even if individual tests fail:**
```bash
./scripts/run-scale-tests.sh --continue-on-failure
```

### Test Execution Options

| Option | Description |
|--------|-------------|
| `--namespace` | Kubernetes namespace (default: default) |
| `--config` | Test matrix configuration file |
| `--resource-config` | Resource configuration file |
| `--output-dir` | Results output directory |
| `--dry-run` | Show execution plan without running tests |
| `--skip-validation` | Skip pre-test validation |
| `--resource-config-only` | Test only specific resource configuration |
| `--prompt-length-only` | Test only specific prompt length |
| `--qps-only` | Test only specific QPS level |
| `--continue-on-failure` | Continue on individual test failures |
| `--cleanup-on-exit` | Cleanup resources on script exit |
| `--verbose` | Enable verbose output |

## Analysis and Reporting

### Pareto Frontier Analysis

Generate Pareto frontier curves showing trade-offs between prompt length and QPS:

```bash
./analysis/pareto-analysis.py \
  --results-dir tools/scale-testing/results \
  --output-dir tools/scale-testing/analysis-output
```

**Outputs:**
- Pareto frontier plots for different metric combinations
- Resource configuration comparison charts
- Processed results CSV file
- Summary analysis JSON

### Performance Reports

Generate comprehensive performance reports with resource sizing recommendations:

```bash
./analysis/performance-reports.py \
  --results-dir tools/scale-testing/analysis-output \
  --output-dir tools/scale-testing/reports
```

**Outputs:**
- Interactive HTML performance report
- Performance heatmaps by resource configuration
- Resource sizing recommendations
- Threshold violation analysis

## Understanding Results

### Key Metrics

- **Throughput QPS**: Actual requests per second achieved
- **Latency P95/P99**: 95th/99th percentile response latency
- **Error Rate**: Percentage of failed requests
- **Memory Usage**: EPP container memory consumption
- **CPU Usage**: EPP container CPU utilization

### Performance Thresholds

The framework uses these default thresholds for analysis:

- Latency P95: ≤ 5000ms
- Latency P99: ≤ 10000ms  
- Error Rate: ≤ 1%
- Memory Utilization: ≤ 80%
- CPU Utilization: ≤ 80%

### Interpreting Pareto Frontiers

Pareto frontier curves show optimal trade-off points where:
- No other configuration achieves better performance in all dimensions
- Moving along the curve represents trading one metric for another
- Points inside the curve represent suboptimal configurations

## Resource Sizing Recommendations

Based on test results, the framework provides recommendations for:

### Production Workload Sizing

- **Light workloads** (< 100 QPS, < 1024 token prompts): Use `default` configuration
- **Medium workloads** (100-300 QPS, 1024-2048 token prompts): Use `high` configuration  
- **Heavy workloads** (> 300 QPS, > 2048 token prompts): Use `maximum` configuration

### Scaling Guidelines

1. **CPU-bound scenarios**: Scale CPU limits first
2. **Memory-bound scenarios**: Scale memory limits for large prompts
3. **Mixed workloads**: Use balanced resource allocation
4. **Auto-scaling**: Base scaling decisions on QPS and latency metrics

## Troubleshooting

### Common Issues

**Test execution fails:**
- Verify Kubernetes connectivity: `kubectl cluster-info`
- Check EPP deployment status: `kubectl get deployment vllm-llama3-8b-instruct-epp`
- Validate gateway accessibility: `kubectl get gateway inference-gateway`

**Resource update failures:**
- Check RBAC permissions for deployment updates
- Verify resource configuration syntax in YAML files
- Monitor cluster resource availability

**Analysis failures:**
- Ensure test results directory contains valid data
- Check Python dependencies: `pip install -r requirements.txt`
- Verify result file formats and structure

### Debugging

Enable verbose logging for detailed execution information:

```bash
./scripts/run-scale-tests.sh --verbose
./analysis/pareto-analysis.py --verbose
```

## Integration with Existing Infrastructure

The framework integrates with existing benchmark infrastructure:

- **LPG Compatibility**: Uses same Latency Profile Generator tool
- **Result Formats**: Compatible with existing benchmark analysis notebooks
- **Metrics Collection**: Integrates with Prometheus monitoring
- **Storage**: Uses consistent result storage patterns

## Quick Start

For immediate testing with default settings:

```bash
# 1. Navigate to scale testing directory
cd tools/scale-testing

# 2. Install dependencies
pip install -r requirements.txt

# 3. Run a quick test with minimal configuration
./scripts/run-scale-tests.sh --resource-config-only default --prompt-length-only medium --qps-only low-50

# 4. Generate analysis
./analysis/pareto-analysis.py --results-dir results --output-dir analysis-output
./analysis/performance-reports.py --results-dir analysis-output --output-dir reports

# 5. View results
open reports/performance_report.html
```

## Contributing

When extending the framework:

1. **Add new test dimensions**: Update `test-matrix.yaml`
2. **Add new resource configs**: Update `resource-configs.yaml`
3. **Add new metrics**: Update analysis scripts
4. **Add new visualizations**: Extend reporting tools

## Performance Baseline

Expected performance characteristics for reference EPP deployment:

- **Default config**: ~200 QPS sustainable, ~2s P95 latency
- **High config**: ~500 QPS sustainable, ~1.5s P95 latency
- **Maximum config**: ~1000 QPS sustainable, ~1s P95 latency

*Note: Actual performance depends on cluster resources, network conditions, and workload characteristics.*
