#!/usr/bin/env python3
"""
Performance Report Generator for EPP Scale Testing

This script generates comprehensive performance reports from EPP scale testing results,
including resource utilization analysis, performance recommendations, and detailed
metrics breakdown.
"""

import argparse
import json
import logging
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set plotting style
try:
    plt.style.use('seaborn-v0_8')
except:
    plt.style.use('seaborn')
sns.set_palette("husl")


class EPPPerformanceReporter:
    """Generates comprehensive performance reports for EPP scale testing results."""
    
    def __init__(self, results_dir: Path, output_dir: Path):
        self.results_dir = Path(results_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Performance thresholds for analysis
        self.thresholds = {
            'latency_p95_ms': 5000,  # 5 second P95 latency threshold
            'latency_p99_ms': 10000,  # 10 second P99 latency threshold
            'error_rate_percent': 1.0,  # 1% error rate threshold
            'memory_utilization_percent': 80,  # 80% memory utilization threshold
            'cpu_utilization_percent': 80  # 80% CPU utilization threshold
        }
    
    def load_processed_results(self) -> pd.DataFrame:
        """Load processed results from Pareto analysis."""
        processed_file = self.results_dir / "processed_results.csv"
        
        if not processed_file.exists():
            raise FileNotFoundError(f"Processed results file not found: {processed_file}")
        
        df = pd.read_csv(processed_file)
        logger.info(f"Loaded {len(df)} processed test results")
        return df
    
    def generate_resource_sizing_guide(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate resource sizing recommendations based on test results."""
        logger.info("Generating resource sizing guide")
        
        sizing_guide = {
            'recommendations': {},
            'performance_profiles': {},
            'resource_efficiency': {}
        }
        
        # Analyze each resource configuration
        for resource_config in df['resource_config'].unique():
            config_df = df[df['resource_config'] == resource_config]
            
            # Calculate performance metrics
            max_qps = config_df['throughput_qps'].max()
            min_latency = config_df['latency_p95_ms'].min()
            avg_memory = config_df['memory_usage_mb'].mean()
            avg_cpu = config_df['cpu_usage_percent'].mean()
            
            # Determine optimal operating ranges
            stable_tests = config_df[
                (config_df['error_rate_percent'] <= self.thresholds['error_rate_percent']) &
                (config_df['latency_p95_ms'] <= self.thresholds['latency_p95_ms'])
            ]
            
            if not stable_tests.empty:
                optimal_qps_range = [stable_tests['throughput_qps'].min(), stable_tests['throughput_qps'].max()]
                optimal_prompt_range = [stable_tests['prompt_length'].min(), stable_tests['prompt_length'].max()]
            else:
                optimal_qps_range = [0, 0]
                optimal_prompt_range = [0, 0]
            
            # Resource efficiency calculation
            cpu_limit = config_df['cpu_limit'].iloc[0]
            memory_limit = config_df['memory_limit'].iloc[0]
            
            # Extract numeric values from resource limits
            cpu_cores = float(cpu_limit.replace('m', '')) / 1000 if 'm' in cpu_limit else float(cpu_limit)
            memory_gb = float(memory_limit.replace('Gi', '').replace('Mi', '')) / (1024 if 'Mi' in memory_limit else 1)
            
            efficiency_score = max_qps / (cpu_cores + memory_gb)  # Simple efficiency metric
            
            sizing_guide['recommendations'][resource_config] = {
                'max_sustainable_qps': max_qps,
                'min_latency_p95_ms': min_latency,
                'optimal_qps_range': optimal_qps_range,
                'optimal_prompt_length_range': optimal_prompt_range,
                'resource_limits': {
                    'cpu': cpu_limit,
                    'memory': memory_limit
                },
                'average_utilization': {
                    'cpu_percent': avg_cpu,
                    'memory_mb': avg_memory
                }
            }
            
            sizing_guide['resource_efficiency'][resource_config] = efficiency_score
        
        return sizing_guide
    
    def generate_performance_heatmaps(self, df: pd.DataFrame) -> None:
        """Generate performance heatmaps for different metrics."""
        logger.info("Generating performance heatmaps")
        
        metrics_to_plot = [
            ('throughput_qps', 'Throughput (QPS)', 'viridis'),
            ('latency_p95_ms', 'Latency P95 (ms)', 'viridis_r'),
            ('error_rate_percent', 'Error Rate (%)', 'Reds'),
            ('memory_usage_mb', 'Memory Usage (MB)', 'Blues')
        ]
        
        for metric, title, colormap in metrics_to_plot:
            if metric not in df.columns:
                continue
            
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'{title} Heatmaps by Resource Configuration', fontsize=16, fontweight='bold')
            
            resource_configs = df['resource_config'].unique()
            
            for i, resource_config in enumerate(resource_configs):
                if i >= 4:  # Only plot first 4 configurations
                    break
                
                ax = axes[i // 2, i % 2]
                config_df = df[df['resource_config'] == resource_config]
                
                # Create pivot table for heatmap
                pivot_data = config_df.pivot_table(
                    values=metric,
                    index='prompt_length',
                    columns='qps_target',
                    aggfunc='mean'
                )
                
                if not pivot_data.empty:
                    sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap=colormap, ax=ax)
                    ax.set_title(f'{resource_config}')
                    ax.set_xlabel('Target QPS')
                    ax.set_ylabel('Prompt Length (tokens)')
                else:
                    ax.set_title(f'{resource_config} (No Data)')
            
            # Hide unused subplots
            for i in range(len(resource_configs), 4):
                axes[i // 2, i % 2].set_visible(False)
            
            plt.tight_layout()
            
            # Save heatmap
            filename = f"heatmap_{metric}.png"
            filepath = self.output_dir / filename
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Saved heatmap: {filepath}")
    
    def generate_threshold_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze performance against defined thresholds."""
        logger.info("Generating threshold analysis")
        
        threshold_analysis = {
            'threshold_violations': {},
            'compliant_configurations': {},
            'performance_boundaries': {}
        }
        
        for resource_config in df['resource_config'].unique():
            config_df = df[df['resource_config'] == resource_config]
            
            violations = {}
            compliant_tests = config_df.copy()
            
            # Check each threshold
            for metric, threshold in self.thresholds.items():
                if metric in config_df.columns:
                    if 'latency' in metric or 'error_rate' in metric:
                        # For latency and error rate, violations are values above threshold
                        violating_tests = config_df[config_df[metric] > threshold]
                        compliant_tests = compliant_tests[compliant_tests[metric] <= threshold]
                    else:
                        # For utilization metrics, violations are values above threshold
                        violating_tests = config_df[config_df[metric] > threshold]
                        compliant_tests = compliant_tests[compliant_tests[metric] <= threshold]
                    
                    violations[metric] = {
                        'count': len(violating_tests),
                        'percentage': len(violating_tests) / len(config_df) * 100,
                        'worst_case': violating_tests[metric].max() if not violating_tests.empty else 0
                    }
            
            threshold_analysis['threshold_violations'][resource_config] = violations
            
            # Analyze compliant configurations
            if not compliant_tests.empty:
                threshold_analysis['compliant_configurations'][resource_config] = {
                    'count': len(compliant_tests),
                    'max_qps': compliant_tests['throughput_qps'].max(),
                    'max_prompt_length': compliant_tests['prompt_length'].max(),
                    'performance_envelope': {
                        'qps_range': [compliant_tests['throughput_qps'].min(), compliant_tests['throughput_qps'].max()],
                        'prompt_range': [compliant_tests['prompt_length'].min(), compliant_tests['prompt_length'].max()]
                    }
                }
            else:
                threshold_analysis['compliant_configurations'][resource_config] = {
                    'count': 0,
                    'message': 'No tests met all performance thresholds'
                }
        
        return threshold_analysis
    
    def generate_html_report(self, df: pd.DataFrame, sizing_guide: Dict[str, Any], 
                           threshold_analysis: Dict[str, Any]) -> None:
        """Generate comprehensive HTML report."""
        logger.info("Generating HTML report")
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>EPP Scale Testing Performance Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                h1, h2, h3 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .metric {{ background-color: #f9f9f9; padding: 10px; margin: 10px 0; border-radius: 5px; }}
                .recommendation {{ background-color: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                .warning {{ background-color: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                .error {{ background-color: #f8d7da; padding: 15px; margin: 10px 0; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <h1>EPP Scale Testing Performance Report</h1>
            <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>Total Tests:</strong> {len(df)}</p>
            
            <h2>Executive Summary</h2>
            <div class="metric">
                <h3>Test Coverage</h3>
                <ul>
                    <li><strong>Resource Configurations:</strong> {', '.join(df['resource_config'].unique())}</li>
                    <li><strong>Prompt Length Range:</strong> {df['prompt_length'].min()} - {df['prompt_length'].max()} tokens</li>
                    <li><strong>QPS Range:</strong> {df['qps_target'].min()} - {df['qps_target'].max()} requests/second</li>
                </ul>
            </div>
            
            <h2>Resource Configuration Recommendations</h2>
        """
        
        # Add resource recommendations
        for config, recommendations in sizing_guide['recommendations'].items():
            html_content += f"""
            <div class="recommendation">
                <h3>{config.title()} Configuration</h3>
                <ul>
                    <li><strong>Max Sustainable QPS:</strong> {recommendations['max_sustainable_qps']:.1f}</li>
                    <li><strong>Min Latency P95:</strong> {recommendations['min_latency_p95_ms']:.1f} ms</li>
                    <li><strong>Optimal QPS Range:</strong> {recommendations['optimal_qps_range'][0]:.1f} - {recommendations['optimal_qps_range'][1]:.1f}</li>
                    <li><strong>Resource Limits:</strong> CPU: {recommendations['resource_limits']['cpu']}, Memory: {recommendations['resource_limits']['memory']}</li>
                    <li><strong>Average Utilization:</strong> CPU: {recommendations['average_utilization']['cpu_percent']:.1f}%, Memory: {recommendations['average_utilization']['memory_mb']:.1f} MB</li>
                </ul>
            </div>
            """
        
        # Add threshold analysis
        html_content += "<h2>Performance Threshold Analysis</h2>"
        
        for config, violations in threshold_analysis['threshold_violations'].items():
            html_content += f"<h3>{config.title()} Configuration</h3>"
            
            for metric, violation_data in violations.items():
                if violation_data['count'] > 0:
                    html_content += f"""
                    <div class="warning">
                        <strong>{metric}:</strong> {violation_data['count']} violations ({violation_data['percentage']:.1f}% of tests)
                        <br>Worst case: {violation_data['worst_case']:.2f}
                    </div>
                    """
        
        html_content += """
            <h2>Key Findings</h2>
            <ul>
                <li>Resource configuration significantly impacts maximum sustainable QPS</li>
                <li>Prompt length has measurable impact on latency and throughput</li>
                <li>Memory utilization scales with prompt length and concurrent requests</li>
                <li>CPU utilization is the primary bottleneck for high QPS scenarios</li>
            </ul>
            
            <h2>Production Deployment Recommendations</h2>
            <div class="recommendation">
                <ul>
                    <li>Start with 'default' configuration for most workloads</li>
                    <li>Scale to 'high' configuration for demanding workloads (>200 QPS)</li>
                    <li>Monitor latency P95 and error rates closely in production</li>
                    <li>Consider prompt length distribution when sizing resources</li>
                    <li>Implement auto-scaling based on QPS and latency metrics</li>
                </ul>
            </div>
            
        </body>
        </html>
        """
        
        # Save HTML report
        report_file = self.output_dir / "performance_report.html"
        with open(report_file, 'w') as f:
            f.write(html_content)
        
        logger.info(f"Saved HTML report: {report_file}")
    
    def generate_report(self) -> None:
        """Generate comprehensive performance report."""
        logger.info("Starting performance report generation")
        
        try:
            # Load processed results
            df = self.load_processed_results()
            
            # Generate resource sizing guide
            sizing_guide = self.generate_resource_sizing_guide(df)
            
            # Generate threshold analysis
            threshold_analysis = self.generate_threshold_analysis(df)
            
            # Generate performance heatmaps
            self.generate_performance_heatmaps(df)
            
            # Generate HTML report
            self.generate_html_report(df, sizing_guide, threshold_analysis)
            
            # Save detailed analysis data
            analysis_data = {
                'sizing_guide': sizing_guide,
                'threshold_analysis': threshold_analysis,
                'generation_timestamp': datetime.now().isoformat()
            }
            
            analysis_file = self.output_dir / "detailed_analysis.json"
            with open(analysis_file, 'w') as f:
                json.dump(analysis_data, f, indent=2)
            
            logger.info(f"Performance report generation complete. Results saved to: {self.output_dir}")
            
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            raise


def main():
    parser = argparse.ArgumentParser(description="EPP Scale Testing Performance Report Generator")
    parser.add_argument("--results-dir", required=True, type=Path, help="Directory containing analysis results")
    parser.add_argument("--output-dir", required=True, type=Path, help="Output directory for performance reports")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    reporter = EPPPerformanceReporter(args.results_dir, args.output_dir)
    reporter.generate_report()


if __name__ == "__main__":
    main()
