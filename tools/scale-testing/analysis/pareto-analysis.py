#!/usr/bin/env python3
"""
Pareto Frontier Analysis for EPP Scale Testing

This script analyzes EPP scale testing results to generate Pareto frontier curves
showing the trade-offs between prompt length and QPS performance.
"""

import argparse
import json
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from scipy.spatial import ConvexHull
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set plotting style
try:
    plt.style.use('seaborn-v0_8')
except:
    plt.style.use('seaborn')
sns.set_palette("husl")


class EPPParetoAnalyzer:
    """Analyzes EPP scale testing results and generates Pareto frontier analysis."""
    
    def __init__(self, results_dir: Path, output_dir: Path):
        self.results_dir = Path(results_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Performance metrics to analyze
        self.metrics = [
            'throughput_qps',
            'latency_p50_ms',
            'latency_p95_ms',
            'latency_p99_ms',
            'error_rate_percent',
            'memory_usage_mb',
            'cpu_usage_percent'
        ]
    
    def load_test_results(self) -> pd.DataFrame:
        """Load all test results from the results directory."""
        logger.info(f"Loading test results from {self.results_dir}")
        
        results = []
        
        # Walk through results directory structure
        for resource_dir in self.results_dir.iterdir():
            if not resource_dir.is_dir():
                continue
                
            resource_config = resource_dir.name
            
            for prompt_dir in resource_dir.iterdir():
                if not prompt_dir.is_dir():
                    continue
                    
                prompt_length_name = prompt_dir.name
                
                for qps_dir in prompt_dir.iterdir():
                    if not qps_dir.is_dir():
                        continue
                        
                    qps_name = qps_dir.name
                    
                    # Load test metadata
                    metadata_file = qps_dir / "test-metadata.json"
                    if not metadata_file.exists():
                        logger.warning(f"Metadata file not found: {metadata_file}")
                        continue
                    
                    try:
                        with open(metadata_file, 'r') as f:
                            metadata = json.load(f)
                    except Exception as e:
                        logger.error(f"Failed to load metadata from {metadata_file}: {e}")
                        continue
                    
                    # Load benchmark results
                    result_files = list(qps_dir.glob("*.json"))
                    benchmark_results = None
                    
                    for result_file in result_files:
                        if result_file.name != "test-metadata.json":
                            try:
                                with open(result_file, 'r') as f:
                                    benchmark_results = json.load(f)
                                break
                            except Exception as e:
                                logger.warning(f"Failed to load result file {result_file}: {e}")
                                continue
                    
                    if not benchmark_results:
                        logger.warning(f"No valid benchmark results found in {qps_dir}")
                        continue
                    
                    # Extract metrics from benchmark results
                    metrics = self.extract_metrics(benchmark_results, metadata)
                    if metrics:
                        results.append(metrics)
        
        if not results:
            raise ValueError("No valid test results found")
        
        df = pd.DataFrame(results)
        logger.info(f"Loaded {len(df)} test results")
        return df
    
    def extract_metrics(self, benchmark_results: Dict[str, Any], metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract performance metrics from benchmark results."""
        try:
            config = metadata['test_configuration']
            
            # Extract basic configuration
            metrics = {
                'resource_config': config['resource_config'],
                'prompt_length': config['prompt_length'],
                'prompt_length_name': config['prompt_length_name'],
                'qps_target': config['qps'],
                'qps_name': config['qps_name'],
                'cpu_limit': config['cpu_limit'],
                'memory_limit': config['memory_limit'],
                'test_duration': config['test_duration']
            }
            
            # Extract performance metrics from benchmark results
            # Note: The exact structure depends on the LPG output format
            if 'summary' in benchmark_results:
                summary = benchmark_results['summary']
                
                metrics.update({
                    'throughput_qps': summary.get('throughput_qps', 0),
                    'latency_p50_ms': summary.get('latency_p50_ms', 0),
                    'latency_p95_ms': summary.get('latency_p95_ms', 0),
                    'latency_p99_ms': summary.get('latency_p99_ms', 0),
                    'error_rate_percent': summary.get('error_rate_percent', 0),
                    'total_requests': summary.get('total_requests', 0),
                    'successful_requests': summary.get('successful_requests', 0)
                })
            
            # Extract resource utilization if available
            if 'resource_utilization' in benchmark_results:
                resource_util = benchmark_results['resource_utilization']
                metrics.update({
                    'memory_usage_mb': resource_util.get('memory_usage_mb', 0),
                    'cpu_usage_percent': resource_util.get('cpu_usage_percent', 0)
                })
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to extract metrics: {e}")
            return None
    
    def calculate_pareto_frontier(self, df: pd.DataFrame, x_col: str, y_col: str, maximize_both: bool = False) -> pd.DataFrame:
        """Calculate Pareto frontier for two metrics."""
        logger.info(f"Calculating Pareto frontier for {x_col} vs {y_col}")
        
        # Prepare data
        data = df[[x_col, y_col]].dropna()
        if data.empty:
            logger.warning(f"No valid data for {x_col} vs {y_col}")
            return pd.DataFrame()
        
        # Convert to numpy array
        points = data.values
        
        if maximize_both:
            # For maximization problems, find points that are not dominated
            pareto_mask = np.ones(len(points), dtype=bool)
            for i, point in enumerate(points):
                if pareto_mask[i]:
                    # Check if this point is dominated by any other point
                    dominated = np.all(points >= point, axis=1) & np.any(points > point, axis=1)
                    pareto_mask[dominated] = False
        else:
            # For mixed optimization (e.g., maximize QPS, minimize latency)
            # Assume x should be maximized, y should be minimized
            pareto_mask = np.ones(len(points), dtype=bool)
            for i, point in enumerate(points):
                if pareto_mask[i]:
                    # Point is dominated if another point has higher x and lower y
                    dominated = (points[:, 0] >= point[0]) & (points[:, 1] <= point[1]) & \
                               ((points[:, 0] > point[0]) | (points[:, 1] < point[1]))
                    pareto_mask[dominated] = False
        
        pareto_points = data[pareto_mask].copy()
        pareto_points = pareto_points.sort_values(x_col)
        
        logger.info(f"Found {len(pareto_points)} Pareto optimal points")
        return pareto_points
    
    def generate_pareto_plots(self, df: pd.DataFrame) -> None:
        """Generate Pareto frontier plots for different metric combinations."""
        logger.info("Generating Pareto frontier plots")
        
        # Define key metric combinations for analysis
        plot_configs = [
            {
                'x': 'prompt_length',
                'y': 'throughput_qps',
                'title': 'Prompt Length vs Throughput QPS',
                'xlabel': 'Prompt Length (tokens)',
                'ylabel': 'Throughput (QPS)',
                'maximize_both': False  # Maximize QPS, prompt length is just a dimension
            },
            {
                'x': 'throughput_qps',
                'y': 'latency_p95_ms',
                'title': 'Throughput vs Latency (P95)',
                'xlabel': 'Throughput (QPS)',
                'ylabel': 'Latency P95 (ms)',
                'maximize_both': False  # Maximize QPS, minimize latency
            },
            {
                'x': 'prompt_length',
                'y': 'latency_p95_ms',
                'title': 'Prompt Length vs Latency (P95)',
                'xlabel': 'Prompt Length (tokens)',
                'ylabel': 'Latency P95 (ms)',
                'maximize_both': False
            }
        ]
        
        for config in plot_configs:
            self.create_pareto_plot(df, config)
    
    def create_pareto_plot(self, df: pd.DataFrame, config: Dict[str, Any]) -> None:
        """Create a single Pareto frontier plot."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(config['title'], fontsize=16, fontweight='bold')
        
        # Plot for each resource configuration
        resource_configs = df['resource_config'].unique()
        colors = sns.color_palette("husl", len(resource_configs))
        
        for i, resource_config in enumerate(resource_configs):
            ax = axes[i // 2, i % 2]
            
            # Filter data for this resource configuration
            config_df = df[df['resource_config'] == resource_config]
            
            if config_df.empty:
                ax.set_title(f"{resource_config} (No Data)")
                continue
            
            # Plot all points
            ax.scatter(config_df[config['x']], config_df[config['y']], 
                      alpha=0.6, color=colors[i], label='All Tests')
            
            # Calculate and plot Pareto frontier
            pareto_points = self.calculate_pareto_frontier(
                config_df, config['x'], config['y'], config['maximize_both']
            )
            
            if not pareto_points.empty:
                ax.plot(pareto_points[config['x']], pareto_points[config['y']], 
                       'o-', color=colors[i], linewidth=2, markersize=8, 
                       label='Pareto Frontier', alpha=0.8)
            
            ax.set_title(f"{resource_config}")
            ax.set_xlabel(config['xlabel'])
            ax.set_ylabel(config['ylabel'])
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # Hide unused subplot if we have fewer than 4 resource configs
        for i in range(len(resource_configs), 4):
            axes[i // 2, i % 2].set_visible(False)
        
        plt.tight_layout()
        
        # Save plot
        filename = f"pareto_{config['x']}_vs_{config['y']}.png"
        filepath = self.output_dir / filename
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved Pareto plot: {filepath}")
    
    def generate_resource_comparison(self, df: pd.DataFrame) -> None:
        """Generate resource configuration comparison plots."""
        logger.info("Generating resource configuration comparison")
        
        # Create comparison plots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Resource Configuration Performance Comparison', fontsize=16, fontweight='bold')
        
        metrics_to_plot = [
            ('throughput_qps', 'Throughput (QPS)'),
            ('latency_p95_ms', 'Latency P95 (ms)'),
            ('memory_usage_mb', 'Memory Usage (MB)'),
            ('cpu_usage_percent', 'CPU Usage (%)')
        ]
        
        for i, (metric, label) in enumerate(metrics_to_plot):
            ax = axes[i // 2, i % 2]
            
            # Create box plot for each resource configuration
            if metric in df.columns:
                sns.boxplot(data=df, x='resource_config', y=metric, ax=ax)
                ax.set_title(label)
                ax.set_xlabel('Resource Configuration')
                ax.set_ylabel(label)
                plt.setp(ax.get_xticklabels(), rotation=45)
            else:
                ax.set_title(f"{label} (No Data)")
        
        plt.tight_layout()
        
        # Save plot
        filepath = self.output_dir / "resource_comparison.png"
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved resource comparison plot: {filepath}")
    
    def generate_summary_report(self, df: pd.DataFrame) -> None:
        """Generate summary report with key findings."""
        logger.info("Generating summary report")
        
        report = {
            'test_summary': {
                'total_tests': len(df),
                'resource_configurations': df['resource_config'].unique().tolist(),
                'prompt_lengths': sorted(df['prompt_length'].unique().tolist()),
                'qps_levels': sorted(df['qps_target'].unique().tolist())
            },
            'performance_summary': {},
            'recommendations': []
        }
        
        # Calculate performance summary for each resource configuration
        for resource_config in df['resource_config'].unique():
            config_df = df[df['resource_config'] == resource_config]
            
            summary = {
                'max_throughput_qps': config_df['throughput_qps'].max(),
                'min_latency_p95_ms': config_df['latency_p95_ms'].min(),
                'avg_memory_usage_mb': config_df['memory_usage_mb'].mean(),
                'avg_cpu_usage_percent': config_df['cpu_usage_percent'].mean(),
                'error_rate_range': [config_df['error_rate_percent'].min(), config_df['error_rate_percent'].max()]
            }
            
            report['performance_summary'][resource_config] = summary
        
        # Generate recommendations
        best_throughput_config = df.loc[df['throughput_qps'].idxmax(), 'resource_config']
        best_latency_config = df.loc[df['latency_p95_ms'].idxmin(), 'resource_config']
        
        report['recommendations'] = [
            f"For maximum throughput: Use '{best_throughput_config}' configuration",
            f"For minimum latency: Use '{best_latency_config}' configuration",
            "Consider prompt length impact on performance when sizing resources",
            "Monitor error rates at high QPS levels for production deployment"
        ]
        
        # Save report
        report_file = self.output_dir / "summary_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Saved summary report: {report_file}")
    
    def run_analysis(self) -> None:
        """Run complete Pareto frontier analysis."""
        logger.info("Starting EPP Pareto frontier analysis")
        
        try:
            # Load test results
            df = self.load_test_results()
            
            # Generate Pareto frontier plots
            self.generate_pareto_plots(df)
            
            # Generate resource comparison
            self.generate_resource_comparison(df)
            
            # Generate summary report
            self.generate_summary_report(df)
            
            # Save processed data
            data_file = self.output_dir / "processed_results.csv"
            df.to_csv(data_file, index=False)
            logger.info(f"Saved processed data: {data_file}")
            
            logger.info(f"Analysis complete. Results saved to: {self.output_dir}")
            
        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            raise


def main():
    parser = argparse.ArgumentParser(description="EPP Scale Testing Pareto Frontier Analysis")
    parser.add_argument("--results-dir", required=True, type=Path, help="Directory containing test results")
    parser.add_argument("--output-dir", required=True, type=Path, help="Output directory for analysis results")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    analyzer = EPPParetoAnalyzer(args.results_dir, args.output_dir)
    analyzer.run_analysis()


if __name__ == "__main__":
    main()
