# EPP Scale Testing Requirements
# Python dependencies for scale testing analysis and reporting tools

# Core data analysis
pandas>=1.5.0
numpy>=1.21.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Scientific computing
scipy>=1.8.0

# YAML processing
PyYAML>=6.0

# Command line argument parsing (built-in, but listed for completeness)
# argparse - built-in

# Path handling (built-in, but listed for completeness)  
# pathlib - built-in

# JSON handling (built-in, but listed for completeness)
# json - built-in

# Logging (built-in, but listed for completeness)
# logging - built-in

# Date/time handling (built-in, but listed for completeness)
# datetime - built-in

# Type hints (built-in in Python 3.5+, but listed for completeness)
# typing - built-in

# Subprocess handling (built-in, but listed for completeness)
# subprocess - built-in

# Warnings handling (built-in, but listed for completeness)
# warnings - built-in
