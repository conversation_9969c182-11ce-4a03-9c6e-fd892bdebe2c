# Kind cluster configuration for EPP scale testing
# This configuration creates a multi-node cluster suitable for testing EPP performance

kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
name: epp-scale-testing

# Cluster networking configuration
networking:
  # Disable default CNI to install our own
  disableDefaultCNI: false
  # Set pod and service subnets
  podSubnet: "**********/16"
  serviceSubnet: "*********/12"
  # API server port
  apiServerPort: 6443

# Node configuration for scale testing
nodes:
# Control plane node
- role: control-plane
  image: kindest/node:v1.28.0
  kubeadmConfigPatches:
  - |
    kind: InitConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        node-labels: "ingress-ready=true"
  extraPortMappings:
  # Gateway/Ingress ports
  - containerPort: 80
    hostPort: 80
    protocol: TCP
  - containerPort: 443
    hostPort: 443
    protocol: TCP
  # EPP metrics port
  - containerPort: 9090
    hostPort: 9090
    protocol: TCP
  # Additional ports for testing
  - containerPort: 8080
    hostPort: 8080
    protocol: TCP
  - containerPort: 8081
    hostPort: 8081
    protocol: TCP

# Worker nodes for distributed testing
- role: worker
  image: kindest/node:v1.28.0
  kubeadmConfigPatches:
  - |
    kind: JoinConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        node-labels: "node-type=worker"

- role: worker
  image: kindest/node:v1.28.0
  kubeadmConfigPatches:
  - |
    kind: JoinConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        node-labels: "node-type=worker"

# Feature gates and runtime configuration
kubeadmConfigPatches:
- |
  kind: ClusterConfiguration
  apiServer:
    extraArgs:
      enable-admission-plugins: NodeRestriction,MutatingAdmissionWebhook,ValidatingAdmissionWebhook
  controllerManager:
    extraArgs:
      bind-address: 0.0.0.0
  scheduler:
    extraArgs:
      bind-address: 0.0.0.0
- |
  kind: KubeProxyConfiguration
  metricsBindAddress: 0.0.0.0
