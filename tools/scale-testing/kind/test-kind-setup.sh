#!/bin/bash

# Quick test script for Kind cluster EPP setup
# This script runs a minimal test to verify the EPP scale testing setup works

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCALE_TEST_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"
CLUSTER_NAME="epp-scale-testing"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if cluster is ready
check_cluster_ready() {
    log_info "Checking if Kind cluster is ready..."
    
    # Check if cluster exists
    if ! kind get clusters | grep -q "^${CLUSTER_NAME}$"; then
        log_error "Cluster $CLUSTER_NAME not found. Please run setup-kind-cluster.sh first."
        exit 1
    fi
    
    # Set kubectl context
    kubectl config use-context "kind-${CLUSTER_NAME}"
    
    # Check if all required deployments are ready
    local deployments=("vllm-llama3-8b-instruct" "vllm-llama3-8b-instruct-epp")
    
    for deployment in "${deployments[@]}"; do
        if ! kubectl get deployment "$deployment" &>/dev/null; then
            log_error "Deployment $deployment not found"
            exit 1
        fi
        
        if ! kubectl wait --timeout=60s --for=condition=available deployment/"$deployment"; then
            log_error "Deployment $deployment is not ready"
            exit 1
        fi
    done
    
    # Check gateway
    if ! kubectl get gateway inference-gateway &>/dev/null; then
        log_error "Gateway inference-gateway not found"
        exit 1
    fi
    
    log_success "Cluster is ready for testing"
}

# Test basic connectivity
test_connectivity() {
    log_info "Testing basic connectivity..."
    
    # Get gateway IP
    local gateway_ip
    gateway_ip=$(kubectl get gateway inference-gateway -o jsonpath='{.status.addresses[0].value}' 2>/dev/null || echo "")
    
    if [[ -z "$gateway_ip" ]]; then
        log_error "Could not get gateway IP"
        return 1
    fi
    
    log_info "Gateway IP: $gateway_ip"
    
    # Test health endpoint
    log_info "Testing health endpoint..."
    if kubectl run test-curl --rm -i --restart=Never --image=curlimages/curl -- \
        curl -s -f "http://${gateway_ip}/health" -m 10; then
        log_success "Health endpoint accessible"
    else
        log_warning "Health endpoint test failed, but continuing..."
    fi
    
    return 0
}

# Run minimal scale test
run_minimal_test() {
    log_info "Running minimal scale test..."
    
    cd "$SCALE_TEST_DIR"
    
    # Create a very minimal test configuration
    cat > /tmp/minimal-test-config.yaml << EOF
metadata:
  name: "kind-minimal-test"
  version: "v1.0.0"
  description: "Minimal test for Kind cluster validation"

execution:
  test_duration_seconds: 60
  warmup_duration_seconds: 10
  cooldown_duration_seconds: 10
  max_parallel_tests: 1
  retry_attempts: 1

prompt_lengths:
  - name: "small"
    tokens: 256
    description: "Small prompt for quick testing"

qps_levels:
  - name: "low-10"
    qps: 10
    category: "low"
    description: "Very low load for validation"

resource_configs:
  - name: "default"
    description: "Default resource allocation"
    cpu_limit: "1000m"
    cpu_request: "500m"
    memory_limit: "1Gi"
    memory_request: "512Mi"
    expected_performance: "medium"

benchmark_config:
  base_config:
    backend: "vllm"
    port: "80"
    tokenizer: "meta-llama/Llama-3.1-8B-Instruct"
    models: "meta-llama/Llama-3.1-8B-Instruct"
    output_length: "256"
    file_prefix: "kind-test"
    prompt_dataset_file: "ShareGPT_V3_unfiltered_cleaned_split.json"

test_execution:
  execution_order: "resource_first"
  skip_combinations: []

metrics:
  collection_interval_seconds: 10
  retention_period_hours: 1
  epp_metrics:
    - "epp_request_duration_seconds"
    - "epp_requests_total"

analysis:
  pareto_analysis:
    primary_metric: "qps_achieved"
    secondary_metric: "prompt_length"
    optimization_direction: "maximize"
  performance_thresholds:
    latency_p95_ms: 5000
    latency_p99_ms: 10000
    error_rate_percent: 5.0
    memory_utilization_percent: 90
    cpu_utilization_percent: 90

output:
  results_directory: "tools/scale-testing/kind-test-results"
  report_formats: ["json"]
  include_raw_data: true
  generate_plots: false
EOF
    
    # Run the test with dry-run first
    log_info "Running dry-run test..."
    if ./scripts/run-scale-tests.sh --config /tmp/minimal-test-config.yaml --dry-run; then
        log_success "Dry-run test passed"
    else
        log_error "Dry-run test failed"
        return 1
    fi
    
    # Check EPP resource manager
    log_info "Testing EPP resource manager..."
    if python3 ./scripts/epp-resource-manager.py \
        --get-resources \
        --config-path ./configs/resource-configs.yaml; then
        log_success "EPP resource manager working"
    else
        log_error "EPP resource manager failed"
        return 1
    fi
    
    log_success "Minimal scale test validation passed"
}

# Test analysis tools
test_analysis_tools() {
    log_info "Testing analysis tools..."
    
    cd "$SCALE_TEST_DIR"
    
    # Check Python dependencies
    log_info "Checking Python dependencies..."
    if python3 -c "import pandas, numpy, matplotlib, seaborn, yaml"; then
        log_success "Python dependencies available"
    else
        log_warning "Some Python dependencies missing. Installing..."
        pip3 install -r requirements.txt
    fi
    
    # Test analysis scripts syntax
    log_info "Testing analysis script syntax..."
    if python3 -m py_compile analysis/pareto-analysis.py; then
        log_success "Pareto analysis script syntax OK"
    else
        log_error "Pareto analysis script has syntax errors"
        return 1
    fi
    
    if python3 -m py_compile analysis/performance-reports.py; then
        log_success "Performance reports script syntax OK"
    else
        log_error "Performance reports script has syntax errors"
        return 1
    fi
    
    log_success "Analysis tools validation passed"
}

# Show cluster information
show_cluster_info() {
    log_info "Cluster Information:"
    echo "===================="
    
    log_info "Cluster nodes:"
    kubectl get nodes -o wide
    
    log_info "EPP and model server pods:"
    kubectl get pods -l app=vllm-llama3-8b-instruct -o wide
    kubectl get pods -l app=vllm-llama3-8b-instruct-epp -o wide
    
    log_info "Gateway status:"
    kubectl get gateway inference-gateway -o yaml | grep -A 10 "status:" || true
    
    log_info "Services:"
    kubectl get svc | grep -E "(vllm|epp|gateway)" || true
    
    # Get gateway IP for testing
    local gateway_ip
    gateway_ip=$(kubectl get gateway inference-gateway -o jsonpath='{.status.addresses[0].value}' 2>/dev/null || echo "")
    
    if [[ -n "$gateway_ip" ]]; then
        log_success "Gateway accessible at: $gateway_ip"
        log_info "Test command:"
        echo "kubectl run test-curl --rm -i --restart=Never --image=curlimages/curl -- \\"
        echo "  curl -X POST http://$gateway_ip/v1/chat/completions \\"
        echo "    -H 'Content-Type: application/json' \\"
        echo "    -d '{\"model\": \"meta-llama/Llama-3.1-8B-Instruct\", \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}]}'"
    fi
}

# Main function
main() {
    log_info "Testing Kind cluster EPP setup"
    
    # Check cluster readiness
    check_cluster_ready
    
    # Test connectivity
    test_connectivity
    
    # Run minimal test
    run_minimal_test
    
    # Test analysis tools
    test_analysis_tools
    
    # Show cluster info
    show_cluster_info
    
    log_success "Kind cluster EPP setup test completed successfully!"
    log_info ""
    log_info "Your Kind cluster is ready for EPP scale testing!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Run full scale tests:"
    log_info "   cd tools/scale-testing"
    log_info "   ./scripts/run-scale-tests.sh --config configs/quick-test-matrix.yaml"
    log_info ""
    log_info "2. Generate analysis:"
    log_info "   ./analysis/pareto-analysis.py --results-dir results --output-dir analysis"
    log_info "   ./analysis/performance-reports.py --results-dir analysis --output-dir reports"
    log_info ""
    log_info "3. View results:"
    log_info "   open reports/performance_report.html"
}

# Run main function
main "$@"
