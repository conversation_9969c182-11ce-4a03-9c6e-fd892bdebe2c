#!/bin/bash

# Setup Kind cluster for EPP scale testing
# This script creates and configures a Kind cluster with all necessary components

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
CLUSTER_NAME="epp-scale-testing"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kind is installed
    if ! command -v kind &> /dev/null; then
        log_error "kind is not installed. Please install kind first:"
        echo "  # On macOS with Homebrew:"
        echo "  brew install kind"
        echo "  # On Linux:"
        echo "  curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-amd64"
        echo "  chmod +x ./kind"
        echo "  sudo mv ./kind /usr/local/bin/kind"
        exit 1
    fi
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install kubectl first:"
        echo "  # On macOS with Homebrew:"
        echo "  brew install kubectl"
        echo "  # On Linux:"
        echo "  curl -LO \"https://dl.k8s.io/release/\$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl\""
        echo "  chmod +x kubectl"
        echo "  sudo mv kubectl /usr/local/bin/"
        exit 1
    fi
    
    # Check if docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Create Kind cluster
create_cluster() {
    log_info "Creating Kind cluster: $CLUSTER_NAME"
    
    # Check if cluster already exists
    if kind get clusters | grep -q "^${CLUSTER_NAME}$"; then
        log_warning "Cluster $CLUSTER_NAME already exists"
        read -p "Do you want to delete and recreate it? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "Deleting existing cluster..."
            kind delete cluster --name "$CLUSTER_NAME"
        else
            log_info "Using existing cluster"
            return 0
        fi
    fi
    
    # Create the cluster
    kind create cluster --config "${SCRIPT_DIR}/kind-cluster-config.yaml" --wait 300s
    
    # Set kubectl context
    kubectl cluster-info --context "kind-${CLUSTER_NAME}"
    
    log_success "Kind cluster created successfully"
}

# Install Gateway API CRDs
install_gateway_api() {
    log_info "Installing Gateway API CRDs..."
    
    # Install Gateway API CRDs
    kubectl apply -f https://github.com/kubernetes-sigs/gateway-api/releases/download/v1.0.0/standard-install.yaml
    
    # Wait for CRDs to be ready
    kubectl wait --for condition=established --timeout=60s crd/gateways.gateway.networking.k8s.io
    kubectl wait --for condition=established --timeout=60s crd/httproutes.gateway.networking.k8s.io
    
    log_success "Gateway API CRDs installed"
}

# Install Envoy Gateway
install_envoy_gateway() {
    log_info "Installing Envoy Gateway..."
    
    # Install Envoy Gateway
    kubectl apply -f https://github.com/envoyproxy/gateway/releases/download/v1.0.0/install.yaml
    
    # Wait for Envoy Gateway to be ready
    kubectl wait --timeout=300s --for=condition=available deployment/envoy-gateway -n envoy-gateway-system
    kubectl wait --timeout=300s --for=condition=programmed gatewayclass/eg
    
    log_success "Envoy Gateway installed"
}

# Deploy llm-d sim server
deploy_sim_server() {
    log_info "Deploying llm-d sim server..."
    
    # Apply the sim server deployment
    kubectl apply -f "${PROJECT_ROOT}/config/manifests/vllm/sim-deployment.yaml"
    
    # Wait for deployment to be ready
    kubectl wait --timeout=300s --for=condition=available deployment/vllm-llama3-8b-instruct
    
    log_success "llm-d sim server deployed"
}

# Deploy Gateway API Inference Extension
deploy_inference_extension() {
    log_info "Deploying Gateway API Inference Extension..."
    
    # Apply inference extension resources
    kubectl apply -f "${PROJECT_ROOT}/config/manifests/inferencepool-resources.yaml"
    
    # Wait for EPP deployment to be ready
    kubectl wait --timeout=300s --for=condition=available deployment/vllm-llama3-8b-instruct-epp
    
    log_success "Gateway API Inference Extension deployed"
}

# Create inference gateway
create_inference_gateway() {
    log_info "Creating inference gateway..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: inference-gateway
  namespace: default
spec:
  gatewayClassName: eg
  listeners:
  - name: http
    port: 80
    protocol: HTTP
    allowedRoutes:
      namespaces:
        from: Same
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: inference-route
  namespace: default
spec:
  parentRefs:
  - name: inference-gateway
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: vllm-llama3-8b-instruct
      port: 8000
EOF
    
    # Wait for gateway to be ready
    kubectl wait --timeout=300s --for=condition=programmed gateway/inference-gateway
    
    log_success "Inference gateway created"
}

# Setup monitoring (optional)
setup_monitoring() {
    log_info "Setting up basic monitoring..."
    
    # Create a simple metrics service for EPP
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Service
metadata:
  name: epp-metrics
  namespace: default
  labels:
    app: vllm-llama3-8b-instruct-epp
spec:
  selector:
    app: vllm-llama3-8b-instruct-epp
  ports:
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP
EOF
    
    log_success "Basic monitoring setup complete"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check all pods are running
    log_info "Checking pod status..."
    kubectl get pods -o wide
    
    # Check gateway status
    log_info "Checking gateway status..."
    kubectl get gateway inference-gateway -o yaml | grep -A 5 "status:"
    
    # Get gateway IP
    local gateway_ip
    gateway_ip=$(kubectl get gateway inference-gateway -o jsonpath='{.status.addresses[0].value}' 2>/dev/null || echo "")
    
    if [[ -n "$gateway_ip" ]]; then
        log_success "Gateway IP: $gateway_ip"
        log_info "You can test the gateway with:"
        echo "  curl -X POST http://$gateway_ip/v1/chat/completions \\"
        echo "    -H 'Content-Type: application/json' \\"
        echo "    -d '{\"model\": \"meta-llama/Llama-3.1-8B-Instruct\", \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}]}'"
    else
        log_warning "Gateway IP not yet available. It may take a few more minutes."
    fi
    
    log_success "Deployment verification complete"
}

# Create HuggingFace token secret (if needed)
create_hf_secret() {
    log_info "Creating HuggingFace token secret..."
    
    if [[ -n "${HF_TOKEN:-}" ]]; then
        kubectl create secret generic hf-token --from-literal=token="$HF_TOKEN" --dry-run=client -o yaml | kubectl apply -f -
        log_success "HuggingFace token secret created"
    else
        log_warning "HF_TOKEN environment variable not set. Creating dummy secret."
        kubectl create secret generic hf-token --from-literal=token="dummy-token" --dry-run=client -o yaml | kubectl apply -f -
        log_info "Note: Set HF_TOKEN environment variable for actual model access"
    fi
}

# Main function
main() {
    log_info "Setting up Kind cluster for EPP scale testing"
    
    # Check prerequisites
    check_prerequisites
    
    # Create cluster
    create_cluster
    
    # Install components
    install_gateway_api
    install_envoy_gateway
    
    # Create HF secret
    create_hf_secret
    
    # Deploy applications
    deploy_sim_server
    deploy_inference_extension
    create_inference_gateway
    
    # Setup monitoring
    setup_monitoring
    
    # Verify deployment
    verify_deployment
    
    log_success "Kind cluster setup complete!"
    log_info "Cluster name: $CLUSTER_NAME"
    log_info "To delete the cluster later, run: kind delete cluster --name $CLUSTER_NAME"
    log_info ""
    log_info "Next steps:"
    log_info "1. Wait for all pods to be ready: kubectl get pods -w"
    log_info "2. Run scale tests: cd tools/scale-testing && ./scripts/run-scale-tests.sh --config configs/quick-test-matrix.yaml"
    log_info "3. Generate analysis: ./analysis/pareto-analysis.py --results-dir results --output-dir analysis"
}

# Run main function
main "$@"
