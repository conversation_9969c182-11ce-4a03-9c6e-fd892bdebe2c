# Kind集群EPP规模测试指南

这个目录包含了用于创建和配置Kind集群来测试EPP规模测试框架的所有必要文件。

## 📋 前置要求

### 系统要求
- **Docker**: 确保Docker已安装并运行
- **Kind**: Kubernetes in Docker工具
- **kubectl**: Kubernetes命令行工具
- **Python 3.8+**: 用于分析工具

### 安装必要工具

**macOS (使用Homebrew):**
```bash
# 安装Docker Desktop (如果还没安装)
# 从 https://www.docker.com/products/docker-desktop 下载安装

# 安装Kind和kubectl
brew install kind kubectl

# 验证安装
kind version
kubectl version --client
docker --version
```

**Linux:**
```bash
# 安装Kind
curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-amd64
chmod +x ./kind
sudo mv ./kind /usr/local/bin/kind

# 安装kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
chmod +x kubectl
sudo mv kubectl /usr/local/bin/

# 验证安装
kind version
kubectl version --client
```

## 🚀 快速开始

### 1. 创建Kind集群

```bash
# 进入项目根目录
cd /path/to/gateway-api-inference-extension

# 运行集群设置脚本
./tools/scale-testing/kind/setup-kind-cluster.sh
```

这个脚本会：
- ✅ 创建一个3节点的Kind集群 (1个控制节点 + 2个工作节点)
- ✅ 安装Gateway API CRDs
- ✅ 部署Envoy Gateway
- ✅ 部署llm-d sim server
- ✅ 部署EPP (Endpoint Picker Plugin)
- ✅ 创建推理网关
- ✅ 设置基础监控

### 2. 验证集群设置

```bash
# 运行验证脚本
./tools/scale-testing/kind/test-kind-setup.sh
```

这个脚本会验证：
- 集群连接性
- 所有组件是否正常运行
- EPP资源管理器功能
- 分析工具依赖

### 3. 运行规模测试

```bash
# 进入规模测试目录
cd tools/scale-testing

# 安装Python依赖
pip install -r requirements.txt

# 运行快速测试
./scripts/run-scale-tests.sh --config configs/quick-test-matrix.yaml

# 或运行完整测试
./scripts/run-scale-tests.sh
```

### 4. 生成分析报告

```bash
# 生成Pareto前沿分析
./analysis/pareto-analysis.py --results-dir results --output-dir analysis

# 生成性能报告
./analysis/performance-reports.py --results-dir analysis --output-dir reports

# 查看HTML报告
open reports/performance_report.html
```

## 📁 文件说明

### `kind-cluster-config.yaml`
Kind集群配置文件，定义了：
- **多节点配置**: 1个控制节点 + 2个工作节点
- **端口映射**: 暴露必要的服务端口
- **网络配置**: Pod和Service子网设置
- **节点标签**: 用于调度和识别

### `setup-kind-cluster.sh`
自动化集群设置脚本，包含：
- 前置条件检查
- Kind集群创建
- Gateway API和Envoy Gateway安装
- 应用部署和配置
- 验证和状态检查

### `test-kind-setup.sh`
集群验证脚本，用于：
- 检查集群就绪状态
- 测试基础连接性
- 验证EPP功能
- 测试分析工具

## 🔧 集群配置详情

### 节点配置
```yaml
控制节点:
  - 角色: control-plane
  - 镜像: kindest/node:v1.28.0
  - 端口映射: 80, 443, 8080, 8081, 9090

工作节点 x2:
  - 角色: worker
  - 镜像: kindest/node:v1.28.0
  - 标签: node-type=worker
```

### 网络配置
```yaml
Pod子网: **********/16
Service子网: *********/12
API服务器端口: 6443
```

### 部署的组件
- **Gateway API CRDs**: v1.0.0
- **Envoy Gateway**: v1.0.0
- **llm-d sim server**: 模拟LLM服务器
- **EPP**: Endpoint Picker Plugin
- **推理网关**: HTTP路由配置

## 🧪 测试场景

### 快速验证测试
```bash
# 最小配置测试 (约5分钟)
./scripts/run-scale-tests.sh --config configs/quick-test-matrix.yaml
```
- 提示长度: 256, 1024 tokens
- QPS: 20, 100
- 资源配置: default
- 测试时长: 2分钟/测试

### 完整规模测试
```bash
# 完整测试矩阵 (约2-3小时)
./scripts/run-scale-tests.sh
```
- 提示长度: 128-8192 tokens (6个级别)
- QPS: 10-1000 (9个级别)
- 资源配置: 4种配置
- 测试时长: 5分钟/测试

## 📊 预期结果

### 性能基准
基于Kind集群的预期性能特征：

| 资源配置 | 最大QPS | P95延迟 | 内存使用 |
|---------|---------|---------|----------|
| minimal | ~50     | ~4s     | ~300MB   |
| default | ~100    | ~3s     | ~600MB   |
| high    | ~200    | ~2s     | ~1.2GB   |
| maximum | ~300    | ~1.5s   | ~2.4GB   |

*注意: Kind集群性能受限于本地资源，实际生产环境性能会更高*

### 分析输出
测试完成后会生成：
- **Pareto前沿图**: 显示提示长度vs QPS权衡
- **性能热图**: 不同配置下的性能表现
- **资源利用率分析**: CPU和内存使用模式
- **HTML报告**: 综合性能分析报告

## 🛠️ 故障排除

### 常见问题

**1. 集群创建失败**
```bash
# 检查Docker状态
docker info

# 清理并重新创建
kind delete cluster --name epp-scale-testing
./tools/scale-testing/kind/setup-kind-cluster.sh
```

**2. Pod无法启动**
```bash
# 检查Pod状态
kubectl get pods -o wide

# 查看Pod日志
kubectl logs deployment/vllm-llama3-8b-instruct
kubectl logs deployment/vllm-llama3-8b-instruct-epp
```

**3. 网关无法访问**
```bash
# 检查网关状态
kubectl get gateway inference-gateway -o yaml

# 检查Envoy Gateway
kubectl get pods -n envoy-gateway-system
```

**4. 测试执行失败**
```bash
# 启用详细日志
./scripts/run-scale-tests.sh --verbose --dry-run

# 检查EPP资源管理器
python3 ./scripts/epp-resource-manager.py --get-resources --config-path ./configs/resource-configs.yaml
```

### 调试命令

```bash
# 查看集群状态
kubectl cluster-info
kubectl get nodes
kubectl get pods --all-namespaces

# 查看网关和路由
kubectl get gateway,httproute
kubectl describe gateway inference-gateway

# 查看EPP指标
kubectl port-forward svc/epp-metrics 9090:9090
curl http://localhost:9090/metrics

# 测试连接性
kubectl run test-curl --rm -i --restart=Never --image=curlimages/curl -- \
  curl -v http://inference-gateway.default.svc.cluster.local/health
```

## 🧹 清理

### 删除集群
```bash
# 删除Kind集群
kind delete cluster --name epp-scale-testing

# 清理测试结果 (可选)
rm -rf tools/scale-testing/results
rm -rf tools/scale-testing/analysis
rm -rf tools/scale-testing/reports
```

### 保留结果删除集群
```bash
# 只删除集群，保留测试结果
kind delete cluster --name epp-scale-testing
```

## 📈 下一步

1. **运行基准测试**: 使用Kind集群建立性能基准
2. **优化配置**: 根据测试结果调整EPP资源配置
3. **生产验证**: 在真实集群中验证Kind集群的发现
4. **持续集成**: 将测试集成到CI/CD流水线

## 💡 提示

- **资源限制**: Kind集群受限于本地机器资源，适合功能验证而非性能基准
- **网络延迟**: 本地网络延迟很低，生产环境延迟会更高
- **并发限制**: 本地测试的并发能力有限，生产环境需要更多资源
- **存储**: 测试结果存储在本地，注意磁盘空间使用

这个Kind集群设置为EPP规模测试提供了一个完整的本地测试环境，让你可以快速验证和开发测试框架！🚀
