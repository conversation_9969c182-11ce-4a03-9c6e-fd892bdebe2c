# EPP Scale Testing Benchmark Template
# This template is used to generate specific benchmark configurations for each test combination

apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: epp-scale-test-benchmark
  name: epp-scale-test-benchmark
spec:
  replicas: 1
  selector:
    matchLabels:
      app: epp-scale-test-benchmark
  template:
    metadata:
      labels:
        app: epp-scale-test-benchmark
    spec:
      containers:
      # Using the same LPG image as existing benchmarks for consistency
      - image: 'us-docker.pkg.dev/cloud-tpu-images/inference/inference-benchmark@sha256:1c100b0cc949c7df7a2db814ae349c790f034b4b373aaad145e77e815e838438'
        imagePullPolicy: Always
        name: epp-scale-test-benchmark
        command:
        - bash
        - -c
        - ./latency_throughput_curve.sh
        env:
        # These values will be dynamically replaced by the test controller
        - name: IP
          value: '${TARGET_IP}'
        - name: REQUEST_RATES
          value: '${QPS_VALUE}'
        - name: BENCHMARK_TIME_SECONDS
          value: '${TEST_DURATION}'
        - name: TOKENIZER
          value: 'meta-llama/Llama-3.1-8B-Instruct'
        - name: MODELS
          value: 'meta-llama/Llama-3.1-8B-Instruct'
        - name: BACKEND
          value: vllm
        - name: PORT
          value: "80"
        - name: INPUT_LENGTH
          value: '${PROMPT_LENGTH}'
        - name: OUTPUT_LENGTH
          value: '1024'  # Fixed output length for consistent comparison
        - name: FILE_PREFIX
          value: 'epp-scale-test-${RESOURCE_CONFIG}-${PROMPT_LENGTH_NAME}-${QPS_NAME}'
        - name: PROMPT_DATASET_FILE
          value: ShareGPT_V3_unfiltered_cleaned_split.json
        # Additional metadata for result tracking
        - name: TEST_METADATA_RESOURCE_CONFIG
          value: '${RESOURCE_CONFIG}'
        - name: TEST_METADATA_PROMPT_LENGTH
          value: '${PROMPT_LENGTH}'
        - name: TEST_METADATA_PROMPT_LENGTH_NAME
          value: '${PROMPT_LENGTH_NAME}'
        - name: TEST_METADATA_QPS
          value: '${QPS_VALUE}'
        - name: TEST_METADATA_QPS_NAME
          value: '${QPS_NAME}'
        - name: TEST_METADATA_CPU_LIMIT
          value: '${CPU_LIMIT}'
        - name: TEST_METADATA_MEMORY_LIMIT
          value: '${MEMORY_LIMIT}'
        - name: HF_TOKEN
          valueFrom:
            secretKeyRef:
              key: token
              name: hf-token
        resources:
          limits:
            cpu: "2"
            memory: 20Gi
          requests:
            cpu: "2"
            memory: 20Gi
        # Add volume mount for custom result collection script
        volumeMounts:
        - name: result-collection-script
          mountPath: /scripts
      volumes:
      - name: result-collection-script
        configMap:
          name: epp-scale-test-result-collection
          defaultMode: 0755
      restartPolicy: Never

---
# ConfigMap containing custom result collection script
apiVersion: v1
kind: ConfigMap
metadata:
  name: epp-scale-test-result-collection
data:
  collect-results.sh: |
    #!/bin/bash
    # Enhanced result collection script for EPP scale testing
    
    # Wait for LPG to finish
    echo "Waiting for LPG benchmark to complete..."
    until echo $(kubectl logs deployment/epp-scale-test-benchmark -n ${NAMESPACE:-default}) | grep -q -m 1 "LPG_FINISHED"; do 
      sleep 30
    done
    
    # Get pod name
    BENCHMARK_POD=$(kubectl get pods -l app=epp-scale-test-benchmark -n ${NAMESPACE:-default} -o jsonpath="{.items[0].metadata.name}")
    echo "Collecting results from pod ${BENCHMARK_POD}"
    
    # Create results directory with metadata
    RESULT_DIR="/results/${TEST_METADATA_RESOURCE_CONFIG}/${TEST_METADATA_PROMPT_LENGTH_NAME}/${TEST_METADATA_QPS_NAME}"
    mkdir -p "${RESULT_DIR}"
    
    # Collect benchmark results
    kubectl exec ${BENCHMARK_POD} -n ${NAMESPACE:-default} -- rm -f ShareGPT_V3_unfiltered_cleaned_split.json
    for f in $(kubectl exec ${BENCHMARK_POD} -n ${NAMESPACE:-default} -- /bin/sh -c "ls -1 *.json"); do
      echo "Downloading json file ${f}"
      kubectl cp -n ${NAMESPACE:-default} ${BENCHMARK_POD}:$f ${RESULT_DIR}/$f
    done
    
    # Create metadata file
    cat > "${RESULT_DIR}/test-metadata.json" << EOF
    {
      "test_configuration": {
        "resource_config": "${TEST_METADATA_RESOURCE_CONFIG}",
        "prompt_length": ${TEST_METADATA_PROMPT_LENGTH},
        "prompt_length_name": "${TEST_METADATA_PROMPT_LENGTH_NAME}",
        "qps": ${TEST_METADATA_QPS},
        "qps_name": "${TEST_METADATA_QPS_NAME}",
        "cpu_limit": "${TEST_METADATA_CPU_LIMIT}",
        "memory_limit": "${TEST_METADATA_MEMORY_LIMIT}",
        "test_duration": ${BENCHMARK_TIME_SECONDS},
        "output_length": ${OUTPUT_LENGTH}
      },
      "test_execution": {
        "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "pod_name": "${BENCHMARK_POD}",
        "namespace": "${NAMESPACE:-default}"
      }
    }
    EOF
    
    echo "Results collected successfully in ${RESULT_DIR}"

---
# Job template for executing the scale test
apiVersion: batch/v1
kind: Job
metadata:
  name: epp-scale-test-${RESOURCE_CONFIG}-${PROMPT_LENGTH_NAME}-${QPS_NAME}
  labels:
    app: epp-scale-test
    resource-config: ${RESOURCE_CONFIG}
    prompt-length: ${PROMPT_LENGTH_NAME}
    qps-level: ${QPS_NAME}
spec:
  template:
    metadata:
      labels:
        app: epp-scale-test
        resource-config: ${RESOURCE_CONFIG}
        prompt-length: ${PROMPT_LENGTH_NAME}
        qps-level: ${QPS_NAME}
    spec:
      containers:
      - name: test-executor
        image: 'us-docker.pkg.dev/cloud-tpu-images/inference/inference-benchmark@sha256:1c100b0cc949c7df7a2db814ae349c790f034b4b373aaad145e77e815e838438'
        command:
        - bash
        - -c
        - |
          # Deploy benchmark configuration
          kubectl apply -f /config/benchmark-deployment.yaml
          
          # Wait for benchmark to complete and collect results
          /scripts/collect-results.sh
          
          # Cleanup benchmark deployment
          kubectl delete deployment epp-scale-test-benchmark --ignore-not-found=true
        env:
        - name: NAMESPACE
          value: default
        - name: TARGET_IP
          value: '${TARGET_IP}'
        - name: QPS_VALUE
          value: '${QPS_VALUE}'
        - name: TEST_DURATION
          value: '${TEST_DURATION}'
        - name: PROMPT_LENGTH
          value: '${PROMPT_LENGTH}'
        - name: PROMPT_LENGTH_NAME
          value: '${PROMPT_LENGTH_NAME}'
        - name: QPS_NAME
          value: '${QPS_NAME}'
        - name: RESOURCE_CONFIG
          value: '${RESOURCE_CONFIG}'
        - name: CPU_LIMIT
          value: '${CPU_LIMIT}'
        - name: MEMORY_LIMIT
          value: '${MEMORY_LIMIT}'
        volumeMounts:
        - name: benchmark-config
          mountPath: /config
        - name: result-collection-script
          mountPath: /scripts
        - name: results-volume
          mountPath: /results
      volumes:
      - name: benchmark-config
        configMap:
          name: epp-scale-test-benchmark-config
      - name: result-collection-script
        configMap:
          name: epp-scale-test-result-collection
          defaultMode: 0755
      - name: results-volume
        persistentVolumeClaim:
          claimName: epp-scale-test-results
      restartPolicy: Never
  backoffLimit: 2
