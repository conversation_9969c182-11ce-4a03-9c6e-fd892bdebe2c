# Quick Test Matrix for EPP Scale Testing
# Minimal configuration for rapid testing and validation

metadata:
  name: "epp-quick-test-matrix"
  version: "v1.0.0"
  description: "Quick test matrix for EPP scale testing validation"

# Test execution configuration
execution:
  test_duration_seconds: 120  # 2 minutes per test for quick validation
  warmup_duration_seconds: 15
  cooldown_duration_seconds: 30
  max_parallel_tests: 1
  retry_attempts: 1

# Minimal prompt length test dimensions
prompt_lengths:
  - name: "small"
    tokens: 512
    description: "Small prompt for quick testing"
  - name: "medium"
    tokens: 1024
    description: "Medium prompt for standard testing"

# Minimal QPS test levels
qps_levels:
  - name: "low-20"
    qps: 20
    category: "low"
    description: "Low load for baseline testing"
  - name: "medium-100"
    qps: 100
    category: "medium"
    description: "Medium load testing"

# Minimal resource configurations
resource_configs:
  - name: "default"
    description: "Default resource allocation"
    cpu_limit: "1000m"
    cpu_request: "500m"
    memory_limit: "1Gi"
    memory_request: "512Mi"
    expected_performance: "medium"

# LPG benchmark configuration
benchmark_config:
  base_config:
    backend: "vllm"
    port: "80"
    tokenizer: "meta-llama/Llama-3.1-8B-Instruct"
    models: "meta-llama/Llama-3.1-8B-Instruct"
    output_length: "512"  # Shorter output for quick tests
    file_prefix: "epp-quick-test"
    prompt_dataset_file: "ShareGPT_V3_unfiltered_cleaned_split.json"

# Test execution configuration
test_execution:
  execution_order: "resource_first"
  skip_combinations: []  # No skips for quick test

# Metrics collection
metrics:
  collection_interval_seconds: 10
  retention_period_hours: 2
  
  epp_metrics:
    - "epp_request_duration_seconds"
    - "epp_requests_total"
    - "epp_memory_usage_bytes"
    - "epp_cpu_usage_seconds_total"

# Analysis configuration
analysis:
  pareto_analysis:
    primary_metric: "qps_achieved"
    secondary_metric: "prompt_length"
    optimization_direction: "maximize"
  
  performance_thresholds:
    latency_p95_ms: 3000  # More lenient for quick testing
    latency_p99_ms: 5000
    error_rate_percent: 2.0
    memory_utilization_percent: 85
    cpu_utilization_percent: 85

# Output configuration
output:
  results_directory: "tools/scale-testing/quick-results"
  report_formats: ["json", "html"]
  include_raw_data: true
  generate_plots: true
  plot_formats: ["png"]
