# EPP Scale Testing Configuration Matrix
# This file defines the comprehensive test matrix for EPP scale testing

metadata:
  name: "epp-scale-testing-matrix"
  version: "v1.0.0"
  description: "Comprehensive scale testing matrix for EPP performance analysis"

# Test execution configuration
execution:
  test_duration_seconds: 300  # 5 minutes per test
  warmup_duration_seconds: 30
  cooldown_duration_seconds: 60
  max_parallel_tests: 1  # Run tests sequentially to avoid resource contention
  retry_attempts: 2

# Prompt length test dimensions (in tokens)
prompt_lengths:
  - name: "tiny"
    tokens: 128
    description: "Minimal prompt length for baseline testing"
  - name: "small"
    tokens: 512
    description: "Small prompt length for typical short queries"
  - name: "medium"
    tokens: 1024
    description: "Medium prompt length for standard use cases"
  - name: "large"
    tokens: 2048
    description: "Large prompt length for complex queries"
  - name: "xlarge"
    tokens: 4096
    description: "Extra large prompt length for document processing"
  - name: "xxlarge"
    tokens: 8192
    description: "Maximum prompt length for stress testing"

# QPS (Queries Per Second) test dimensions
qps_levels:
  # Low load testing
  - name: "low-10"
    qps: 10
    category: "low"
    description: "Minimal load for baseline performance"
  - name: "low-20"
    qps: 20
    category: "low"
    description: "Low load testing"
  - name: "low-50"
    qps: 50
    category: "low"
    description: "Moderate low load"
  
  # Medium load testing
  - name: "medium-100"
    qps: 100
    category: "medium"
    description: "Medium load baseline"
  - name: "medium-200"
    qps: 200
    category: "medium"
    description: "Standard medium load"
  - name: "medium-300"
    qps: 300
    category: "medium"
    description: "High medium load"
  
  # High load testing
  - name: "high-500"
    qps: 500
    category: "high"
    description: "High load baseline"
  - name: "high-750"
    qps: 750
    category: "high"
    description: "Very high load"
  - name: "high-1000"
    qps: 1000
    category: "high"
    description: "Maximum load testing"

# EPP resource configuration variants
resource_configs:
  - name: "minimal"
    description: "Minimal resource allocation for cost optimization"
    cpu_limit: "500m"
    cpu_request: "250m"
    memory_limit: "512Mi"
    memory_request: "256Mi"
    expected_performance: "low"
  
  - name: "default"
    description: "Default resource allocation for standard workloads"
    cpu_limit: "1000m"
    cpu_request: "500m"
    memory_limit: "1Gi"
    memory_request: "512Mi"
    expected_performance: "medium"
  
  - name: "high"
    description: "High resource allocation for demanding workloads"
    cpu_limit: "2000m"
    cpu_request: "1000m"
    memory_limit: "2Gi"
    memory_request: "1Gi"
    expected_performance: "high"
  
  - name: "maximum"
    description: "Maximum resource allocation for stress testing"
    cpu_limit: "4000m"
    cpu_request: "2000m"
    memory_limit: "4Gi"
    memory_request: "2Gi"
    expected_performance: "maximum"

# LPG benchmark configuration templates
benchmark_config:
  base_config:
    backend: "vllm"
    port: "80"
    tokenizer: "meta-llama/Llama-3.1-8B-Instruct"
    models: "meta-llama/Llama-3.1-8B-Instruct"
    output_length: "1024"  # Fixed output length for consistent comparison
    file_prefix: "epp-scale-test"
    prompt_dataset_file: "ShareGPT_V3_unfiltered_cleaned_split.json"
  
  # Dynamic parameters that will be set per test
  dynamic_params:
    - "IP"  # Target IP address
    - "REQUEST_RATES"  # QPS value
    - "INPUT_LENGTH"  # Prompt length in tokens
    - "BENCHMARK_TIME_SECONDS"  # Test duration

# Test execution order and grouping
test_execution:
  # Group tests by resource configuration to minimize deployment changes
  execution_order: "resource_first"  # Options: resource_first, prompt_first, qps_first
  
  # Skip certain combinations that are expected to fail or are not meaningful
  skip_combinations:
    - resource_config: "minimal"
      qps_levels: ["high-750", "high-1000"]
      reason: "Expected to fail due to insufficient resources"
    
    - prompt_lengths: ["xxlarge"]
      qps_levels: ["high-500", "high-750", "high-1000"]
      reason: "Memory constraints with large prompts and high QPS"

# Metrics collection configuration
metrics:
  collection_interval_seconds: 5
  retention_period_hours: 24
  
  # EPP-specific metrics to collect
  epp_metrics:
    - "epp_request_duration_seconds"
    - "epp_requests_total"
    - "epp_memory_usage_bytes"
    - "epp_cpu_usage_seconds_total"
    - "epp_queue_depth"
    - "epp_active_requests"
  
  # System metrics to collect
  system_metrics:
    - "container_memory_usage_bytes"
    - "container_cpu_usage_seconds_total"
    - "container_network_receive_bytes_total"
    - "container_network_transmit_bytes_total"

# Result analysis configuration
analysis:
  # Pareto frontier analysis parameters
  pareto_analysis:
    primary_metric: "qps_achieved"
    secondary_metric: "prompt_length"
    optimization_direction: "maximize"  # Maximize QPS for given prompt length
  
  # Performance thresholds for analysis
  performance_thresholds:
    latency_p95_ms: 5000  # 5 second P95 latency threshold
    latency_p99_ms: 10000  # 10 second P99 latency threshold
    error_rate_percent: 1.0  # 1% error rate threshold
    memory_utilization_percent: 80  # 80% memory utilization threshold
    cpu_utilization_percent: 80  # 80% CPU utilization threshold

# Output configuration
output:
  results_directory: "tools/scale-testing/results"
  report_formats: ["json", "csv", "html"]
  include_raw_data: true
  generate_plots: true
  plot_formats: ["png", "svg"]
