# EPP Resource Configuration Variants
# This file defines different resource allocation configurations for EPP scale testing

apiVersion: v1
kind: ConfigMap
metadata:
  name: epp-scale-testing-resource-configs
  namespace: default
data:
  # Minimal resource configuration
  minimal.yaml: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: vllm-llama3-8b-instruct-epp
      namespace: default
      labels:
        app: vllm-llama3-8b-instruct-epp
        test-config: minimal
    spec:
      replicas: 1
      selector:
        matchLabels:
          app: vllm-llama3-8b-instruct-epp
      template:
        metadata:
          labels:
            app: vllm-llama3-8b-instruct-epp
            test-config: minimal
        spec:
          terminationGracePeriodSeconds: 130
          containers:
          - name: epp
            image: us-central1-docker.pkg.dev/k8s-staging-images/gateway-api-inference-extension/epp:main
            imagePullPolicy: Always
            args:
            - --pool-name
            - "vllm-llama3-8b-instruct"
            - "--pool-namespace"
            - "default"
            - --v
            - "4"
            - --zap-encoder
            - "json"
            - --grpc-port
            - "9002"
            - --grpc-health-port
            - "9003"
            - "--config-file"
            - "/config/default-plugins.yaml"
            ports:
            - containerPort: 9002
            - containerPort: 9003
            - name: metrics
              containerPort: 9090
            resources:
              limits:
                cpu: "500m"
                memory: "512Mi"
              requests:
                cpu: "250m"
                memory: "256Mi"
            volumeMounts:
            - name: plugins-config-volume
              mountPath: "/config"
          volumes:
          - name: plugins-config-volume
            configMap:
              name: epp-plugins-config

  # Default resource configuration
  default.yaml: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: vllm-llama3-8b-instruct-epp
      namespace: default
      labels:
        app: vllm-llama3-8b-instruct-epp
        test-config: default
    spec:
      replicas: 1
      selector:
        matchLabels:
          app: vllm-llama3-8b-instruct-epp
      template:
        metadata:
          labels:
            app: vllm-llama3-8b-instruct-epp
            test-config: default
        spec:
          terminationGracePeriodSeconds: 130
          containers:
          - name: epp
            image: us-central1-docker.pkg.dev/k8s-staging-images/gateway-api-inference-extension/epp:main
            imagePullPolicy: Always
            args:
            - --pool-name
            - "vllm-llama3-8b-instruct"
            - "--pool-namespace"
            - "default"
            - --v
            - "4"
            - --zap-encoder
            - "json"
            - --grpc-port
            - "9002"
            - --grpc-health-port
            - "9003"
            - "--config-file"
            - "/config/default-plugins.yaml"
            ports:
            - containerPort: 9002
            - containerPort: 9003
            - name: metrics
              containerPort: 9090
            resources:
              limits:
                cpu: "1000m"
                memory: "1Gi"
              requests:
                cpu: "500m"
                memory: "512Mi"
            volumeMounts:
            - name: plugins-config-volume
              mountPath: "/config"
          volumes:
          - name: plugins-config-volume
            configMap:
              name: epp-plugins-config

  # High resource configuration
  high.yaml: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: vllm-llama3-8b-instruct-epp
      namespace: default
      labels:
        app: vllm-llama3-8b-instruct-epp
        test-config: high
    spec:
      replicas: 1
      selector:
        matchLabels:
          app: vllm-llama3-8b-instruct-epp
      template:
        metadata:
          labels:
            app: vllm-llama3-8b-instruct-epp
            test-config: high
        spec:
          terminationGracePeriodSeconds: 130
          containers:
          - name: epp
            image: us-central1-docker.pkg.dev/k8s-staging-images/gateway-api-inference-extension/epp:main
            imagePullPolicy: Always
            args:
            - --pool-name
            - "vllm-llama3-8b-instruct"
            - "--pool-namespace"
            - "default"
            - --v
            - "4"
            - --zap-encoder
            - "json"
            - --grpc-port
            - "9002"
            - --grpc-health-port
            - "9003"
            - "--config-file"
            - "/config/default-plugins.yaml"
            ports:
            - containerPort: 9002
            - containerPort: 9003
            - name: metrics
              containerPort: 9090
            resources:
              limits:
                cpu: "2000m"
                memory: "2Gi"
              requests:
                cpu: "1000m"
                memory: "1Gi"
            volumeMounts:
            - name: plugins-config-volume
              mountPath: "/config"
          volumes:
          - name: plugins-config-volume
            configMap:
              name: epp-plugins-config

  # Maximum resource configuration
  maximum.yaml: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: vllm-llama3-8b-instruct-epp
      namespace: default
      labels:
        app: vllm-llama3-8b-instruct-epp
        test-config: maximum
    spec:
      replicas: 1
      selector:
        matchLabels:
          app: vllm-llama3-8b-instruct-epp
      template:
        metadata:
          labels:
            app: vllm-llama3-8b-instruct-epp
            test-config: maximum
        spec:
          terminationGracePeriodSeconds: 130
          containers:
          - name: epp
            image: us-central1-docker.pkg.dev/k8s-staging-images/gateway-api-inference-extension/epp:main
            imagePullPolicy: Always
            args:
            - --pool-name
            - "vllm-llama3-8b-instruct"
            - "--pool-namespace"
            - "default"
            - --v
            - "4"
            - --zap-encoder
            - "json"
            - --grpc-port
            - "9002"
            - --grpc-health-port
            - "9003"
            - "--config-file"
            - "/config/default-plugins.yaml"
            ports:
            - containerPort: 9002
            - containerPort: 9003
            - name: metrics
              containerPort: 9090
            resources:
              limits:
                cpu: "4000m"
                memory: "4Gi"
              requests:
                cpu: "2000m"
                memory: "2Gi"
            volumeMounts:
            - name: plugins-config-volume
              mountPath: "/config"
          volumes:
          - name: plugins-config-volume
            configMap:
              name: epp-plugins-config
