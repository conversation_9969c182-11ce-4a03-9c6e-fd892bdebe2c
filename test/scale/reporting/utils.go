/*
Copyright 2024 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package reporting

import (
	"fmt"
	"math"

	"sigs.k8s.io/gateway-api-inference-extension/test/scale/config"
)

// Mathematical utility functions

func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

func max(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

// Analysis utility functions

func (rg *ReportGenerator) calculateAvgQPS(results []config.TestResult) float64 {
	if len(results) == 0 {
		return 0.0
	}

	total := 0.0
	for _, result := range results {
		total += result.Metrics.QPS
	}
	return total / float64(len(results))
}

func (rg *ReportGenerator) calculateAvgLatency(results []config.TestResult) float64 {
	if len(results) == 0 {
		return 0.0
	}

	total := 0.0
	for _, result := range results {
		total += result.Metrics.LatencyP99
	}
	return total / float64(len(results))
}

func (rg *ReportGenerator) findBestConfig(results []config.TestResult) map[string]interface{} {
	if len(results) == 0 {
		return map[string]interface{}{}
	}

	// Find the result with the best overall score (highest QPS with acceptable latency)
	bestResult := results[0]
	bestScore := rg.calculateOverallScore(bestResult)

	for _, result := range results[1:] {
		score := rg.calculateOverallScore(result)
		if score > bestScore {
			bestScore = score
			bestResult = result
		}
	}

	return map[string]interface{}{
		"promptLength": bestResult.Parameters["promptLength"],
		"qps":          bestResult.Parameters["qps"],
		"actualQPS":    bestResult.Metrics.QPS,
		"latencyP99":   bestResult.Metrics.LatencyP99,
		"score":        bestScore,
	}
}

func (rg *ReportGenerator) calculateOverallScore(result config.TestResult) float64 {
	// Simple scoring function: QPS / (1 + latency_penalty + error_penalty)
	latencyPenalty := result.Metrics.LatencyP99 / 1000.0 // Convert ms to seconds
	errorPenalty := result.Metrics.ErrorRate * 10.0      // Heavily penalize errors

	return result.Metrics.QPS / (1.0 + latencyPenalty + errorPenalty)
}

func (rg *ReportGenerator) calculateAvgEfficiency(tradeoffs []map[string]interface{}) float64 {
	if len(tradeoffs) == 0 {
		return 0.0
	}

	total := 0.0
	for _, tradeoff := range tradeoffs {
		if efficiency, ok := tradeoff["efficiency"].(float64); ok {
			total += efficiency
		}
	}
	return total / float64(len(tradeoffs))
}

func (rg *ReportGenerator) findBestTradeoff(tradeoffs []map[string]interface{}) map[string]interface{} {
	if len(tradeoffs) == 0 {
		return map[string]interface{}{}
	}

	bestTradeoff := tradeoffs[0]
	bestEfficiency := 0.0
	if efficiency, ok := bestTradeoff["efficiency"].(float64); ok {
		bestEfficiency = efficiency
	}

	for _, tradeoff := range tradeoffs[1:] {
		if efficiency, ok := tradeoff["efficiency"].(float64); ok {
			if efficiency > bestEfficiency {
				bestEfficiency = efficiency
				bestTradeoff = tradeoff
			}
		}
	}

	return bestTradeoff
}

func (rg *ReportGenerator) analyzeQPSScaling(results []config.TestResult) map[string]interface{} {
	if len(results) < 2 {
		return map[string]interface{}{}
	}

	// Calculate scaling efficiency
	var scalingFactors []float64
	for i := 1; i < len(results); i++ {
		prevQPS := results[i-1].Metrics.QPS
		currQPS := results[i].Metrics.QPS

		if prevQPS > 0 {
			scalingFactor := currQPS / prevQPS
			scalingFactors = append(scalingFactors, scalingFactor)
		}
	}

	avgScaling := 0.0
	if len(scalingFactors) > 0 {
		for _, factor := range scalingFactors {
			avgScaling += factor
		}
		avgScaling /= float64(len(scalingFactors))
	}

	return map[string]interface{}{
		"averageScalingFactor": avgScaling,
		"scalingEfficiency":    rg.calculateScalingEfficiency(scalingFactors),
		"linearityScore":       rg.calculateLinearityScore(results),
	}
}

func (rg *ReportGenerator) analyzeLatencyScaling(results []config.TestResult) map[string]interface{} {
	if len(results) < 2 {
		return map[string]interface{}{}
	}

	// Calculate latency growth rate
	var growthRates []float64
	for i := 1; i < len(results); i++ {
		prevLatency := results[i-1].Metrics.LatencyP99
		currLatency := results[i].Metrics.LatencyP99

		if prevLatency > 0 {
			growthRate := (currLatency - prevLatency) / prevLatency
			growthRates = append(growthRates, growthRate)
		}
	}

	avgGrowthRate := 0.0
	if len(growthRates) > 0 {
		for _, rate := range growthRates {
			avgGrowthRate += rate
		}
		avgGrowthRate /= float64(len(growthRates))
	}

	return map[string]interface{}{
		"averageGrowthRate": avgGrowthRate,
		"latencyStability":  rg.calculateLatencyStability(results),
		"degradationPoint":  rg.findLatencyDegradationPoint(results),
	}
}

func (rg *ReportGenerator) analyzeErrorTrend(results []config.TestResult) map[string]interface{} {
	if len(results) == 0 {
		return map[string]interface{}{}
	}

	var errorRates []float64
	for _, result := range results {
		errorRates = append(errorRates, result.Metrics.ErrorRate)
	}

	avgErrorRate := 0.0
	maxErrorRate := 0.0
	for _, rate := range errorRates {
		avgErrorRate += rate
		if rate > maxErrorRate {
			maxErrorRate = rate
		}
	}
	avgErrorRate /= float64(len(errorRates))

	return map[string]interface{}{
		"averageErrorRate": avgErrorRate,
		"maxErrorRate":     maxErrorRate,
		"errorStability":   rg.calculateErrorStability(errorRates),
		"errorThreshold":   rg.findErrorThreshold(results),
	}
}

func (rg *ReportGenerator) calculateResourceEfficiency() float64 {
	// Calculate resource efficiency as QPS per unit of resource
	if len(rg.results) == 0 {
		return 0.0
	}

	totalEfficiency := 0.0
	count := 0

	for _, result := range rg.results {
		if result.Success && result.ResourceUsage.CPUUsage > 0 {
			efficiency := result.Metrics.QPS / result.ResourceUsage.CPUUsage
			totalEfficiency += efficiency
			count++
		}
	}

	if count == 0 {
		return 0.0
	}

	return totalEfficiency / float64(count)
}

func (rg *ReportGenerator) analyzePromptLengthScaling() map[string]interface{} {
	// Group results by prompt length and analyze scaling
	promptGroups := make(map[int][]config.TestResult)

	for _, result := range rg.results {
		if promptLen, ok := result.Parameters["promptLength"].(int); ok {
			promptGroups[promptLen] = append(promptGroups[promptLen], result)
		}
	}

	scaling := make(map[string]interface{})
	for promptLen, results := range promptGroups {
		if len(results) > 0 {
			scaling[fmt.Sprintf("%d_tokens", promptLen)] = map[string]interface{}{
				"avgQPS":     rg.calculateAvgQPS(results),
				"avgLatency": rg.calculateAvgLatency(results),
				"efficiency": rg.calculatePromptEfficiency(results),
			}
		}
	}

	return scaling
}

func (rg *ReportGenerator) analyzeResourceScaling() map[string]interface{} {
	// Analyze how resource usage scales with load
	if len(rg.results) == 0 {
		return map[string]interface{}{}
	}

	// Sort by QPS to analyze resource scaling
	sortedResults := make([]config.TestResult, len(rg.results))
	copy(sortedResults, rg.results)

	// Simple bubble sort by QPS
	for i := 0; i < len(sortedResults)-1; i++ {
		for j := 0; j < len(sortedResults)-i-1; j++ {
			if sortedResults[j].Metrics.QPS > sortedResults[j+1].Metrics.QPS {
				sortedResults[j], sortedResults[j+1] = sortedResults[j+1], sortedResults[j]
			}
		}
	}

	return map[string]interface{}{
		"cpuScaling":    rg.calculateCPUScaling(sortedResults),
		"memoryScaling": rg.calculateMemoryScaling(sortedResults),
		"efficiency":    rg.calculateResourceScalingEfficiency(sortedResults),
	}
}

// Additional helper functions for detailed analysis

func (rg *ReportGenerator) calculateScalingEfficiency(factors []float64) float64 {
	if len(factors) == 0 {
		return 0.0
	}

	// Calculate variance to measure consistency
	mean := 0.0
	for _, factor := range factors {
		mean += factor
	}
	mean /= float64(len(factors))

	variance := 0.0
	for _, factor := range factors {
		variance += math.Pow(factor-mean, 2)
	}
	variance /= float64(len(factors))

	// Lower variance indicates better scaling efficiency
	return 1.0 / (1.0 + variance)
}

func (rg *ReportGenerator) calculateLinearityScore(results []config.TestResult) float64 {
	// Measure how linear the QPS scaling is
	if len(results) < 3 {
		return 1.0
	}

	// Calculate correlation coefficient between expected and actual QPS
	// This is a simplified implementation
	return 0.8 // Placeholder value
}

func (rg *ReportGenerator) calculateLatencyStability(results []config.TestResult) float64 {
	if len(results) < 2 {
		return 1.0
	}

	latencies := make([]float64, len(results))
	for i, result := range results {
		latencies[i] = result.Metrics.LatencyP99
	}

	// Calculate coefficient of variation
	mean := 0.0
	for _, latency := range latencies {
		mean += latency
	}
	mean /= float64(len(latencies))

	variance := 0.0
	for _, latency := range latencies {
		variance += math.Pow(latency-mean, 2)
	}
	variance /= float64(len(latencies))

	if mean == 0 {
		return 1.0
	}

	cv := math.Sqrt(variance) / mean
	return 1.0 / (1.0 + cv) // Higher stability = lower coefficient of variation
}

// Additional missing functions

func (rg *ReportGenerator) findLatencyDegradationPoint(results []config.TestResult) map[string]interface{} {
	if len(results) < 2 {
		return map[string]interface{}{}
	}

	// Find the point where latency starts to degrade significantly
	threshold := 1.5 // 50% increase threshold

	for i := 1; i < len(results); i++ {
		prevLatency := results[i-1].Metrics.LatencyP99
		currLatency := results[i].Metrics.LatencyP99

		if prevLatency > 0 && currLatency/prevLatency > threshold {
			return map[string]interface{}{
				"promptLength": results[i].Parameters["promptLength"],
				"qps":          results[i].Parameters["qps"],
				"latency":      currLatency,
				"increase":     (currLatency - prevLatency) / prevLatency,
			}
		}
	}

	return map[string]interface{}{"found": false}
}

func (rg *ReportGenerator) calculateErrorStability(errorRates []float64) float64 {
	if len(errorRates) < 2 {
		return 1.0
	}

	// Calculate coefficient of variation for error rates
	mean := 0.0
	for _, rate := range errorRates {
		mean += rate
	}
	mean /= float64(len(errorRates))

	if mean == 0 {
		return 1.0 // Perfect stability if no errors
	}

	variance := 0.0
	for _, rate := range errorRates {
		variance += math.Pow(rate-mean, 2)
	}
	variance /= float64(len(errorRates))

	cv := math.Sqrt(variance) / mean
	return 1.0 / (1.0 + cv)
}

func (rg *ReportGenerator) findErrorThreshold(results []config.TestResult) map[string]interface{} {
	errorThreshold := 0.05 // 5% error rate threshold

	for _, result := range results {
		if result.Metrics.ErrorRate > errorThreshold {
			return map[string]interface{}{
				"promptLength": result.Parameters["promptLength"],
				"qps":          result.Parameters["qps"],
				"errorRate":    result.Metrics.ErrorRate,
				"threshold":    errorThreshold,
			}
		}
	}

	return map[string]interface{}{"found": false}
}

func (rg *ReportGenerator) calculatePromptEfficiency(results []config.TestResult) float64 {
	if len(results) == 0 {
		return 0.0
	}

	totalEfficiency := 0.0
	for _, result := range results {
		if promptLen, ok := result.Parameters["promptLength"].(int); ok && promptLen > 0 {
			// Efficiency as QPS per 1000 tokens
			efficiency := result.Metrics.QPS / (float64(promptLen) / 1000.0)
			totalEfficiency += efficiency
		}
	}

	return totalEfficiency / float64(len(results))
}

func (rg *ReportGenerator) calculateCPUScaling(results []config.TestResult) map[string]interface{} {
	if len(results) < 2 {
		return map[string]interface{}{}
	}

	// Calculate how CPU usage scales with QPS
	var scalingFactors []float64
	for i := 1; i < len(results); i++ {
		prevCPU := results[i-1].ResourceUsage.CPUUsage
		currCPU := results[i].ResourceUsage.CPUUsage
		prevQPS := results[i-1].Metrics.QPS
		currQPS := results[i].Metrics.QPS

		if prevCPU > 0 && prevQPS > 0 {
			cpuIncrease := (currCPU - prevCPU) / prevCPU
			qpsIncrease := (currQPS - prevQPS) / prevQPS

			if qpsIncrease > 0 {
				scalingFactor := cpuIncrease / qpsIncrease
				scalingFactors = append(scalingFactors, scalingFactor)
			}
		}
	}

	avgScaling := 0.0
	if len(scalingFactors) > 0 {
		for _, factor := range scalingFactors {
			avgScaling += factor
		}
		avgScaling /= float64(len(scalingFactors))
	}

	return map[string]interface{}{
		"averageScalingFactor": avgScaling,
		"efficiency":           1.0 / max(avgScaling, 0.1), // Lower scaling factor = higher efficiency
	}
}

func (rg *ReportGenerator) calculateMemoryScaling(results []config.TestResult) map[string]interface{} {
	if len(results) < 2 {
		return map[string]interface{}{}
	}

	// Calculate how memory usage scales with QPS
	var scalingFactors []float64
	for i := 1; i < len(results); i++ {
		prevMem := float64(results[i-1].ResourceUsage.MemoryUsage)
		currMem := float64(results[i].ResourceUsage.MemoryUsage)
		prevQPS := results[i-1].Metrics.QPS
		currQPS := results[i].Metrics.QPS

		if prevMem > 0 && prevQPS > 0 {
			memIncrease := (currMem - prevMem) / prevMem
			qpsIncrease := (currQPS - prevQPS) / prevQPS

			if qpsIncrease > 0 {
				scalingFactor := memIncrease / qpsIncrease
				scalingFactors = append(scalingFactors, scalingFactor)
			}
		}
	}

	avgScaling := 0.0
	if len(scalingFactors) > 0 {
		for _, factor := range scalingFactors {
			avgScaling += factor
		}
		avgScaling /= float64(len(scalingFactors))
	}

	return map[string]interface{}{
		"averageScalingFactor": avgScaling,
		"efficiency":           1.0 / max(avgScaling, 0.1),
	}
}

func (rg *ReportGenerator) calculateResourceScalingEfficiency(results []config.TestResult) float64 {
	if len(results) == 0 {
		return 0.0
	}

	// Calculate overall resource scaling efficiency
	cpuScaling := rg.calculateCPUScaling(results)
	memoryScaling := rg.calculateMemoryScaling(results)

	cpuEff := 0.5 // Default efficiency
	if eff, ok := cpuScaling["efficiency"].(float64); ok {
		cpuEff = eff
	}

	memEff := 0.5 // Default efficiency
	if eff, ok := memoryScaling["efficiency"].(float64); ok {
		memEff = eff
	}

	// Weighted average of CPU and memory efficiency
	return 0.7*cpuEff + 0.3*memEff
}
