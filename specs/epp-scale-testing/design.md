# EPP Scale Testing Technical Design

## Architecture Overview

The EPP scale testing framework extends the existing benchmark infrastructure to provide comprehensive performance analysis across multiple dimensions: prompt length, QPS (Queries Per Second), and resource configurations.

```mermaid
graph TB
    A[Scale Test Controller] --> B[LPG Benchmark Tool]
    A --> C[EPP Resource Manager]
    A --> D[Metrics Collector]
    
    B --> E[llm-d sim server]
    C --> F[EPP Deployment]
    D --> G[Prometheus/Metrics]
    
    H[Test Configuration Matrix] --> A
    I[Results Aggregator] --> J[Pareto Analysis]
    I --> K[Performance Reports]
    
    D --> I
    G --> I
```

## Technical Stack

- **Testing Framework**: Extended LPG (Latency Profile Generator) infrastructure
- **Model Server**: llm-d sim server for consistent, high-scale testing
- **Metrics Collection**: Prometheus + custom EPP metrics
- **Analysis Tools**: Python-based analysis scripts with matplotlib/seaborn
- **Orchestration**: Kubernetes Jobs for automated test execution
- **Storage**: JSON-based results compatible with existing benchmark tools

## Component Design

### 1. Scale Test Controller

**Purpose**: Orchestrates test execution across the parameter matrix

**Key Features**:
- Manages test configuration matrix (prompt length × QPS × resource configs)
- Coordinates EPP resource updates between test runs
- Handles test sequencing and cleanup
- Integrates with existing benchmark infrastructure

**Implementation**: Kubernetes Job with custom controller logic

### 2. EPP Resource Manager

**Purpose**: Dynamically configures EPP resource limits during testing

**Resource Configurations to Test**:
```yaml
resource_configs:
  - name: "minimal"
    cpu_limit: "500m"
    memory_limit: "512Mi"
  - name: "default"
    cpu_limit: "1000m"
    memory_limit: "1Gi"
  - name: "high"
    cpu_limit: "2000m"
    memory_limit: "2Gi"
  - name: "maximum"
    cpu_limit: "4000m"
    memory_limit: "4Gi"
```

### 3. Test Configuration Matrix

**Prompt Length Dimensions**:
- 128, 512, 1024, 2048, 4096, 8192 tokens

**QPS Dimensions**:
- Low: 10, 20, 50 QPS
- Medium: 100, 200, 300 QPS  
- High: 500, 750, 1000 QPS

**Test Duration**: 300 seconds per configuration (5 minutes)

### 4. Enhanced Metrics Collection

**EPP-Specific Metrics**:
- Request processing latency (P50, P95, P99)
- Memory usage patterns
- CPU utilization
- Queue depth and wait times
- Error rates and timeout counts

**Integration Points**:
- Existing Prometheus metrics from EPP
- LPG benchmark result collection
- Custom resource utilization monitoring

### 5. Pareto Analysis Engine

**Analysis Capabilities**:
- Generate prompt length vs QPS trade-off curves
- Identify optimal operating points for different resource configurations
- Statistical analysis of performance variance
- Resource efficiency calculations

**Output Formats**:
- Interactive plots (matplotlib/plotly)
- CSV data for further analysis
- Summary reports with recommendations

## Data Flow

1. **Test Initialization**:
   - Load test configuration matrix
   - Deploy llm-d sim server with appropriate configuration
   - Initialize metrics collection

2. **Test Execution Loop**:
   ```
   For each resource_config:
     Update EPP deployment with resource limits
     Wait for EPP to stabilize
     For each prompt_length:
       For each qps_target:
         Configure LPG with parameters
         Execute benchmark test
         Collect metrics and results
         Store results with metadata
   ```

3. **Results Processing**:
   - Aggregate results across all test runs
   - Generate Pareto frontier analysis
   - Create performance visualization
   - Generate summary reports

## File Structure

```
tools/scale-testing/
├── configs/
│   ├── resource-configs.yaml
│   ├── test-matrix.yaml
│   └── benchmark-templates/
├── scripts/
│   ├── run-scale-tests.sh
│   ├── epp-resource-manager.py
│   └── results-analyzer.py
├── manifests/
│   ├── scale-test-controller.yaml
│   └── epp-resource-variants/
└── analysis/
    ├── pareto-analysis.py
    ├── performance-reports.py
    └── visualization.py
```

## Integration with Existing Infrastructure

**Compatibility Requirements**:
- Reuse existing LPG benchmark tool and configurations
- Maintain compatibility with current result formats
- Integrate with existing Jupyter notebook analysis workflow
- Preserve existing benchmark download and storage mechanisms

**Extensions**:
- Enhanced test orchestration for multi-dimensional testing
- Additional metadata collection for resource configurations
- Extended analysis capabilities for Pareto frontier generation

## Security and Safety

**Resource Management**:
- Implement resource quotas to prevent cluster resource exhaustion
- Add timeout mechanisms for stuck tests
- Include cleanup procedures for failed test runs

**Data Handling**:
- Secure storage of test results
- Proper cleanup of temporary resources
- Audit logging of test execution

## Testing Strategy

**Unit Tests**:
- Test configuration parsing and validation
- Resource manager functionality
- Analysis algorithm correctness

**Integration Tests**:
- End-to-end test execution with minimal configuration
- Metrics collection and aggregation
- Result format compatibility

**Performance Validation**:
- Baseline performance comparison with existing benchmarks
- Resource usage validation during test execution
